"use client"

import { useState } from "react"
import StatusBar from "@/components/StatusBar"
import MainPage from "@/components/pages/MainPage"
import DetailPage from "@/components/pages/DetailPage"
import RecordModal from "@/components/RecordModal"
import RecordingPage from "@/components/pages/RecordingPage"

export default function App() {
  const [currentPage, setCurrentPage] = useState("main")
  const [selectedRecord, setSelectedRecord] = useState(null)
  const [showRecordModal, setShowRecordModal] = useState(false)

  const renderPage = () => {
    if (currentPage === "detail" && selectedRecord) {
      return <DetailPage record={selectedRecord} onBack={() => setCurrentPage("main")} />
    }

    if (currentPage === "recording") {
      return (
        <RecordingPage
          onBack={() => {
            setCurrentPage("main")
            setShowRecordModal(true)
          }}
          onSave={(content, tags) => {
            // 保存录音转文字结果
            console.log("保存录音内容:", { content, tags })
            alert("录音内容已保存到iCloud！")
            setCurrentPage("main")
          }}
        />
      )
    }

    return (
      <MainPage
        onRecordClick={(record) => {
          setSelectedRecord(record)
          setCurrentPage("detail")
        }}
        onShowRecord={() => setShowRecordModal(true)}
      />
    )
  }

  return (
    <div className="max-w-sm mx-auto bg-black rounded-[2.5rem] p-2 shadow-2xl">
      <div className="bg-white rounded-[2rem] overflow-hidden h-[844px] relative">
        <StatusBar />
        {renderPage()}

        {/* Record Modal */}
        {showRecordModal && (
          <RecordModal
            onClose={() => setShowRecordModal(false)}
            onStartRecording={() => {
              setShowRecordModal(false)
              setCurrentPage("recording")
            }}
          />
        )}

        {/* iOS Home Indicator */}
        <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2">
          <div className="w-32 h-1 bg-black rounded-full opacity-30"></div>
        </div>
      </div>
    </div>
  )
}
