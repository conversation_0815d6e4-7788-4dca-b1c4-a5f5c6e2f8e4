"use client"

import { useState, useRef, useEffect } from "react"
import { <PERSON>, Send, <PERSON>rkles, Save, RefreshCw, Check, XIcon, Copy } from "lucide-react"

interface Record {
  id: string
  content: string
  tags: string[]
  createdAt: string
}

interface AIModalProps {
  records: Record[]
  onClose: () => void
}

interface Message {
  role: "user" | "ai"
  content: string
  id: string
}

export default function AIModal({ records, onClose }: AIModalProps) {
  const [messages, setMessages] = useState<Message[]>([])
  const [currentMessage, setCurrentMessage] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [editingMessageId, setEditingMessageId] = useState<string | null>(null)
  const [editingContent, setEditingContent] = useState("")
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const generateId = () => Date.now().toString() + Math.random().toString(36).substr(2, 9)

  const generateAIResponse = (userMessage: string) => {
    setIsLoading(true)

    setTimeout(() => {
      let response = ""
      const lowerMessage = userMessage.toLowerCase()

      if (lowerMessage.includes("学习") || lowerMessage.includes("技术")) {
        response =
          "根据您的记录分析，您最近在学习SwiftUI状态管理和React Native性能优化。您的学习方式很系统，建议继续保持记录学习笔记的习惯。我注意到您对iOS开发和用户体验都很感兴趣，这是很好的技能组合。"
      } else if (lowerMessage.includes("工作") || lowerMessage.includes("产品")) {
        response =
          "从您的产品会议记录来看，您很关注用户体验优化和功能开发的优先级。您提到团队决定先专注核心功能完善，这是明智的产品策略。您的产品思维和技术背景结合得很好。"
      } else if (lowerMessage.includes("总结")) {
        response = `## 近期记录总结

### 技术学习方面
- **SwiftUI开发**：深入学习状态管理最佳实践
- **React Native**：掌握性能优化和内存管理技巧
- **iOS开发**：持续关注新技术和开发模式

### 产品与工作
- 积极参与产品会议，讨论用户体验优化
- 关注功能开发优先级，推动核心功能完善
- 具备良好的产品思维和技术结合能力

### 个人发展
- 保持良好的学习记录习惯
- 制定周末阅读计划，持续自我提升
- 对AI应用有产品灵感和创新思考

您的学习轨迹显示了技术深度和产品广度的良好结合。`
      } else if (lowerMessage.includes("写") || lowerMessage.includes("帮我")) {
        response = `## 基于您记录的个性化内容

### 学习成长轨迹
通过分析您的记录，我发现您是一个善于学习和总结的技术人员。从SwiftUI的状态管理到React Native的性能优化，每一个技术点都有深入的思考和实践。

### 产品思维培养
您不仅关注技术实现，更注重用户体验和产品策略。在产品会议中的积极参与体现了您的全局思维。

### 未来发展建议
1. 继续保持技术学习的深度
2. 加强产品思维与技术能力的结合
3. 考虑将AI技术应用到实际产品中

这份内容基于您的个人记录定制，可以保存为新的记录。`
      } else {
        response = `关于"${userMessage}"，基于您在iCloud中的记录分析：

您的记录显示了对技术学习和产品设计的持续关注。从具体的技术实践到产品策略思考，都体现了您的专业成长轨迹。

建议您继续保持这种学习和记录的习惯，这将是您职业发展的重要资产。`
      }

      setMessages((prev) => [...prev, { role: "ai", content: response, id: generateId() }])
      setIsLoading(false)
    }, 1500)
  }

  const handleSendMessage = () => {
    if (currentMessage.trim()) {
      const newMessage = { role: "user" as const, content: currentMessage, id: generateId() }
      setMessages((prev) => [...prev, newMessage])
      generateAIResponse(currentMessage)
      setCurrentMessage("")
    }
  }

  const handleEditMessage = (messageId: string, content: string) => {
    setEditingMessageId(messageId)
    setEditingContent(content)
  }

  const handleSaveEdit = () => {
    if (editingMessageId && editingContent.trim()) {
      // 找到编辑的消息索引
      const messageIndex = messages.findIndex((msg) => msg.id === editingMessageId)
      if (messageIndex !== -1) {
        // 更新消息内容
        const updatedMessages = [...messages]
        updatedMessages[messageIndex] = { ...updatedMessages[messageIndex], content: editingContent }

        // 删除该消息之后的所有消息（包括AI回复）
        const newMessages = updatedMessages.slice(0, messageIndex + 1)
        setMessages(newMessages)

        // 重新生成AI回复
        generateAIResponse(editingContent)
      }

      setEditingMessageId(null)
      setEditingContent("")
    }
  }

  const handleCancelEdit = () => {
    setEditingMessageId(null)
    setEditingContent("")
  }

  const handleRegenerateResponse = (messageId: string) => {
    // 找到这个AI消息的前一个用户消息
    const messageIndex = messages.findIndex((msg) => msg.id === messageId)
    if (messageIndex > 0) {
      const previousUserMessage = messages[messageIndex - 1]
      if (previousUserMessage.role === "user") {
        // 删除当前AI回复
        const newMessages = messages.filter((msg) => msg.id !== messageId)
        setMessages(newMessages)
        // 重新生成回复
        generateAIResponse(previousUserMessage.content)
      }
    }
  }

  const handleCopyContent = (content: string) => {
    navigator.clipboard.writeText(content)
    alert("内容已复制到剪贴板")
  }

  const handleSaveToRecords = (content: string) => {
    const newRecord = {
      id: generateId(),
      content: content,
      tags: ["AI生成", "智能整理"],
      createdAt: "刚刚",
    }
    alert("内容已保存到iCloud记录！")
    console.log("保存的记录:", newRecord)
  }

  return (
    <div className="absolute inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="w-full max-w-lg bg-white rounded-3xl max-h-[80vh] overflow-hidden shadow-2xl">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-100">
          <div className="flex items-center">
            <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mr-3">
              <Sparkles className="w-5 h-5 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-900">AI 助手</h2>
              <p className="text-sm text-gray-600">基于您的记录智能对话</p>
            </div>
          </div>
          <button onClick={onClose} className="p-2 hover:bg-gray-100 rounded-full transition-colors">
            <X className="w-6 h-6 text-gray-600" />
          </button>
        </div>

        {/* Messages */}
        <div className="flex-1 p-6 max-h-96 overflow-y-auto">
          {messages.length === 0 ? (
            <div className="text-center py-12">
              <div className="w-16 h-16 bg-gradient-to-r from-purple-100 to-pink-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Sparkles className="w-8 h-8 text-purple-500" />
              </div>
              <h3 className="font-semibold text-gray-800 mb-2">AI智能助手</h3>
              <p className="text-gray-600 text-sm">基于您的记录内容，回答任何问题</p>
            </div>
          ) : (
            <div className="space-y-4">
              {messages.map((message, index) => (
                <div key={message.id} className={`flex ${message.role === "user" ? "justify-end" : "justify-start"}`}>
                  <div className={`max-w-[80%] group`}>
                    {/* Message Content */}
                    {editingMessageId === message.id ? (
                      // Editing mode
                      <div className="space-y-2">
                        <textarea
                          value={editingContent}
                          onChange={(e) => setEditingContent(e.target.value)}
                          className="w-full p-3 border border-gray-300 rounded-2xl resize-none focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                          rows={3}
                          autoFocus
                        />
                        <div className="flex items-center gap-2">
                          <button
                            onClick={handleSaveEdit}
                            className="p-2 bg-green-100 text-green-700 rounded-full hover:bg-green-200 transition-colors"
                            title="保存"
                          >
                            <Check className="w-4 h-4" />
                          </button>
                          <button
                            onClick={handleCancelEdit}
                            className="p-2 bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200 transition-colors"
                            title="取消"
                          >
                            <XIcon className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    ) : (
                      // Normal display mode
                      <>
                        <div
                          className={`px-5 py-3 rounded-3xl transition-all duration-200 ${
                            message.role === "user"
                              ? "bg-purple-500 text-white cursor-pointer hover:bg-purple-600"
                              : "bg-gray-100 text-gray-800"
                          }`}
                          onClick={
                            message.role === "user" ? () => handleEditMessage(message.id, message.content) : undefined
                          }
                        >
                          <div className="whitespace-pre-wrap">{message.content}</div>
                        </div>

                        {/* Action buttons - only show for AI messages */}
                        {message.role === "ai" && (
                          <div className="mt-2 px-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                            <div className="flex items-center gap-1">
                              <button
                                onClick={() => handleRegenerateResponse(message.id)}
                                className="p-2 bg-orange-100 text-orange-700 rounded-full hover:bg-orange-200 transition-colors"
                                disabled={isLoading}
                                title="重新生成"
                              >
                                <RefreshCw className="w-4 h-4" />
                              </button>
                              <button
                                onClick={() => handleCopyContent(message.content)}
                                className="p-2 bg-blue-100 text-blue-700 rounded-full hover:bg-blue-200 transition-colors"
                                title="复制"
                              >
                                <Copy className="w-4 h-4" />
                              </button>
                              <button
                                onClick={() => handleSaveToRecords(message.content)}
                                className="p-2 bg-green-100 text-green-700 rounded-full hover:bg-green-200 transition-colors"
                                title="保存到记录"
                              >
                                <Save className="w-4 h-4" />
                              </button>
                            </div>
                          </div>
                        )}
                      </>
                    )}
                  </div>
                </div>
              ))}

              {isLoading && (
                <div className="flex justify-start">
                  <div className="bg-gray-100 px-5 py-3 rounded-3xl">
                    <div className="flex items-center space-x-2">
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                        <div
                          className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                          style={{ animationDelay: "0.1s" }}
                        ></div>
                        <div
                          className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                          style={{ animationDelay: "0.2s" }}
                        ></div>
                      </div>
                      <span className="text-sm text-gray-600">AI正在思考...</span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
          <div ref={messagesEndRef} />
        </div>

        {/* Input */}
        <div className="p-6 border-t border-gray-100">
          <div className="flex items-center space-x-3">
            <input
              type="text"
              value={currentMessage}
              onChange={(e) => setCurrentMessage(e.target.value)}
              placeholder="问问AI关于您的记录..."
              className="flex-1 py-4 px-5 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white"
              onKeyPress={(e) => e.key === "Enter" && handleSendMessage()}
              disabled={isLoading}
            />
            <button
              onClick={handleSendMessage}
              disabled={isLoading || !currentMessage.trim()}
              className="p-4 bg-purple-500 hover:bg-purple-600 disabled:opacity-50 text-white rounded-2xl transition-colors shadow-md hover:shadow-lg"
            >
              <Send className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
