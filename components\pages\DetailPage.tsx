"use client"

import { useState } from "react"
import { ArrowLeft, Edit3, Save, Share, Trash2, Tag, Clock, Cloud } from "lucide-react"

interface Record {
  id: string
  content: string
  tags: string[]
  createdAt: string
}

interface DetailPageProps {
  record: Record
  onBack: () => void
}

export default function DetailPage({ record, onBack }: DetailPageProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [editedContent, setEditedContent] = useState(record.content)
  const [editedTags, setEditedTags] = useState(record.tags)
  const [newTag, setNewTag] = useState("")
  const [isSaving, setIsSaving] = useState(false)

  const handleSave = () => {
    setIsSaving(true)
    setTimeout(() => {
      setIsEditing(false)
      setIsSaving(false)
      alert("已同步到iCloud！")
    }, 1000)
  }

  const handleAddTag = () => {
    if (newTag.trim() && !editedTags.includes(newTag.trim())) {
      setEditedTags([...editedTags, newTag.trim()])
      setNewTag("")
    }
  }

  const handleRemoveTag = (tagToRemove: string) => {
    setEditedTags(editedTags.filter((tag) => tag !== tagToRemove))
  }

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: "我的记录",
        text: editedContent,
      })
    } else {
      navigator.clipboard.writeText(editedContent)
      alert("内容已复制到剪贴板")
    }
  }

  const handleDelete = () => {
    if (confirm("确定要删除这条记录吗？删除后将从iCloud中移除。")) {
      onBack()
    }
  }

  return (
    <div className="flex flex-col h-full bg-gradient-to-br from-white to-gray-50">
      {/* Header */}
      <div className="flex items-center justify-between p-6 bg-white/80 backdrop-blur-sm border-b border-gray-100">
        <button onClick={onBack} className="p-2 hover:bg-gray-100 rounded-2xl transition-colors">
          <ArrowLeft className="w-6 h-6 text-gray-600" />
        </button>

        <h1 className="text-lg font-semibold text-gray-800">记录详情</h1>

        <div className="flex items-center space-x-2">
          <button
            onClick={() => setIsEditing(!isEditing)}
            className="p-2 hover:bg-gray-100 rounded-2xl transition-colors"
          >
            <Edit3 className="w-5 h-5 text-gray-600" />
          </button>
          <button onClick={handleShare} className="p-2 hover:bg-gray-100 rounded-2xl transition-colors">
            <Share className="w-5 h-5 text-gray-600" />
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 p-6 overflow-y-auto space-y-6 pb-20">
        {/* Meta Info */}
        <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-5 shadow-sm border border-white/50">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="flex items-center text-sm text-gray-600">
                <Clock className="w-4 h-4 mr-1" />
                {record.createdAt}
              </div>
            </div>
            <div className="flex items-center text-xs text-gray-500">
              <Cloud className="w-4 h-4 mr-1" />
              已同步
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-6 shadow-sm border border-white/50">
          <h3 className="font-semibold text-gray-800 mb-4">内容</h3>
          {isEditing ? (
            <textarea
              value={editedContent}
              onChange={(e) => setEditedContent(e.target.value)}
              className="w-full h-48 p-4 border border-gray-200 rounded-2xl resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white/70 backdrop-blur-sm"
            />
          ) : (
            <div className="p-5 bg-gray-50/80 rounded-2xl border border-gray-100">
              <p className="text-gray-800 leading-relaxed whitespace-pre-wrap">{editedContent}</p>
            </div>
          )}
        </div>

        {/* Tags */}
        <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-6 shadow-sm border border-white/50">
          <div className="flex items-center mb-4">
            <Tag className="w-5 h-5 text-gray-600 mr-2" />
            <h3 className="font-semibold text-gray-800">标签</h3>
          </div>

          <div className="flex flex-wrap gap-3 mb-4">
            {editedTags.map((tag, index) => (
              <span
                key={index}
                className={`px-4 py-2 rounded-full text-sm font-medium flex items-center transition-colors ${
                  isEditing
                    ? "bg-red-50 text-red-600 border border-red-200"
                    : "bg-blue-50 text-blue-600 border border-blue-200"
                }`}
              >
                #{tag}
                {isEditing && (
                  <button
                    onClick={() => handleRemoveTag(tag)}
                    className="ml-2 text-red-400 hover:text-red-600 font-bold"
                  >
                    ×
                  </button>
                )}
              </span>
            ))}
          </div>

          {isEditing && (
            <div className="flex items-center space-x-3">
              <input
                type="text"
                value={newTag}
                onChange={(e) => setNewTag(e.target.value)}
                placeholder="添加新标签"
                className="flex-1 py-3 px-4 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white/70 backdrop-blur-sm"
                onKeyPress={(e) => e.key === "Enter" && handleAddTag()}
              />
              <button
                onClick={handleAddTag}
                className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-2xl transition-colors font-medium"
              >
                添加
              </button>
            </div>
          )}
        </div>

        {/* Actions */}
        {isEditing && (
          <div className="space-y-3">
            <button
              onClick={handleSave}
              disabled={isSaving}
              className="w-full bg-blue-500 hover:bg-blue-600 disabled:opacity-50 text-white py-4 px-4 rounded-2xl font-medium transition-colors flex items-center justify-center shadow-md"
            >
              {isSaving ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                  同步中...
                </>
              ) : (
                <>
                  <Save className="w-5 h-5 mr-2" />
                  保存到iCloud
                </>
              )}
            </button>
            <button
              onClick={() => setIsEditing(false)}
              className="w-full py-4 px-4 border border-gray-200 text-gray-600 rounded-2xl font-medium hover:bg-gray-50 transition-colors"
            >
              取消编辑
            </button>
          </div>
        )}

        {!isEditing && (
          <button
            onClick={handleDelete}
            className="w-full bg-red-50 hover:bg-red-100 text-red-600 py-4 px-4 rounded-2xl font-medium transition-colors flex items-center justify-center border border-red-200"
          >
            <Trash2 className="w-5 h-5 mr-2" />
            从iCloud删除
          </button>
        )}
      </div>
    </div>
  )
}
