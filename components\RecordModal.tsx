"use client"

import type React from "react"

import { useState } from "react"
import { X, Mic, Send, Sparkles } from "lucide-react"

interface RecordModalProps {
  onClose: () => void
  onStartRecording: () => void
}

export default function RecordModal({ onClose, onStartRecording }: RecordModalProps) {
  const [textInput, setTextInput] = useState("")
  const [tags, setTags] = useState<string[]>([])
  const [isProcessing, setIsProcessing] = useState(false)
  const [showRecordButton, setShowRecordButton] = useState(true)

  const generateTags = (text: string) => {
    setIsProcessing(true)
    setTimeout(() => {
      setTags(["AI生成", "智能整理", "文字记录"])
      setIsProcessing(false)
    }, 2000)
  }

  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value
    setTextInput(value)

    // 如果用户开始输入文字，隐藏录音按钮
    if (value.trim().length > 0) {
      setShowRecordButton(false)
      if (value.trim().length > 10) {
        generateTags(value)
      }
    } else {
      setShowRecordButton(true)
      setTags([])
    }
  }

  const handleSave = () => {
    if (textInput.trim()) {
      alert("记录已保存到iCloud！")
      onClose()
    }
  }

  return (
    <div className="absolute inset-0 bg-black/50 backdrop-blur-sm flex items-end z-50">
      <div className="w-full bg-white rounded-t-3xl max-h-[85%] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-100">
          <h2 className="text-xl font-bold text-gray-900">新建记录</h2>
          <button onClick={onClose} className="p-2 hover:bg-gray-100 rounded-full transition-colors">
            <X className="w-6 h-6 text-gray-600" />
          </button>
        </div>

        <div className="p-6 space-y-6">
          {/* Voice Recording Section */}
          {showRecordButton && (
            <div className="flex flex-col items-center py-8">
              <div className="relative flex flex-col items-center">
                {/* Main Recording Button */}
                <button
                  onClick={onStartRecording}
                  className="relative w-32 h-32 rounded-full flex items-center justify-center transition-all duration-300 transform bg-gradient-to-br from-blue-500 to-blue-600 shadow-2xl shadow-blue-500/40 hover:scale-105 hover:shadow-3xl hover:shadow-blue-500/50 active:scale-95"
                >
                  {/* Inner Glow Effect */}
                  <div className="absolute inset-2 rounded-full bg-blue-400/30 blur-sm"></div>

                  {/* Icon */}
                  <div className="relative z-10">
                    <Mic className="w-12 h-12 text-white drop-shadow-lg" strokeWidth={2.5} />
                  </div>
                </button>

                {/* Status Text */}
                <div className="mt-6 text-center">
                  <p className="text-lg font-semibold text-gray-800 mb-1">轻触开始语音记录</p>
                  <p className="text-sm text-gray-500">支持实时语音转文字</p>
                </div>
              </div>
            </div>
          )}

          {/* Text Input Section */}
          <div className="space-y-3">
            <h3 className="font-semibold text-gray-800 flex items-center">
              <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
              {showRecordButton ? "或者直接输入文字" : "文字输入"}
            </h3>
            <textarea
              value={textInput}
              onChange={handleTextChange}
              placeholder="输入您的想法和记录..."
              className="w-full h-32 p-4 border border-gray-200 rounded-2xl resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50/50 transition-colors"
            />
          </div>

          {/* AI Tags */}
          {(tags.length > 0 || isProcessing) && (
            <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-3xl p-6 border border-purple-200">
              <div className="flex items-center mb-4">
                <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mr-3">
                  <Sparkles className="w-4 h-4 text-white" />
                </div>
                <h3 className="font-semibold text-gray-800">AI智能标签</h3>
              </div>

              {isProcessing ? (
                <div className="flex items-center text-purple-600">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-purple-500 mr-3"></div>
                  <span className="font-medium">AI正在分析内容...</span>
                </div>
              ) : (
                <div className="flex flex-wrap gap-3">
                  {tags.map((tag, index) => (
                    <span
                      key={index}
                      className="px-4 py-2 bg-gradient-to-r from-purple-100 to-pink-100 text-purple-700 rounded-full text-sm font-medium border border-purple-200 shadow-sm"
                    >
                      #{tag}
                    </span>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Save Button */}
          {textInput.trim() && (
            <button
              onClick={handleSave}
              className="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-semibold py-4 px-6 rounded-2xl flex items-center justify-center transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]"
            >
              <Send className="w-5 h-5 mr-3" />
              保存到iCloud
            </button>
          )}
        </div>
      </div>
    </div>
  )
}
