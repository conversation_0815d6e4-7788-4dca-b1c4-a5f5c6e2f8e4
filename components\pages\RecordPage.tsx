"use client"

import { useState, useRef, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON>c<PERSON>ff, Send, <PERSON>rkles, MoreHorizontal } from "lucide-react"

export default function RecordPage() {
  const [isRecording, setIsRecording] = useState(false)
  const [transcription, setTranscription] = useState("")
  const [textInput, setTextInput] = useState("")
  const [tags, setTags] = useState<string[]>([])
  const [isProcessing, setIsProcessing] = useState(false)
  const [showMenu, setShowMenu] = useState(false)
  const recordingTimer = useRef<NodeJS.Timeout>()
  const [recordingTime, setRecordingTime] = useState(0)

  useEffect(() => {
    if (isRecording) {
      recordingTimer.current = setInterval(() => {
        setRecordingTime((prev) => prev + 1)
      }, 1000)
    } else {
      if (recordingTimer.current) {
        clearInterval(recordingTimer.current)
      }
      setRecordingTime(0)
    }

    return () => {
      if (recordingTimer.current) {
        clearInterval(recordingTimer.current)
      }
    }
  }, [isRecording])

  const handleRecording = () => {
    if (isRecording) {
      setIsRecording(false)
      // 模拟语音转文字
      setTimeout(() => {
        setTranscription(
          "今天的产品会议很有收获，我们讨论了用户体验优化和新功能开发的优先级。团队决定先专注于核心功能的完善，然后再考虑扩展功能。",
        )
        generateTags("今天的产品会议很有收获，我们讨论了用户体验优化和新功能开发的优先级。")
      }, 1000)
    } else {
      setIsRecording(true)
      setTranscription("")
      setTags([])
    }
  }

  const generateTags = (text: string) => {
    setIsProcessing(true)
    // 模拟AI生成标签
    setTimeout(() => {
      setTags(["产品会议", "用户体验", "功能开发", "团队协作"])
      setIsProcessing(false)
    }, 2000)
  }

  const handleSave = () => {
    const content = transcription || textInput
    if (content.trim()) {
      // 保存到iCloud/Core Data
      alert("记录已保存到iCloud！")
      setTranscription("")
      setTextInput("")
      setTags([])
    }
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`
  }

  return (
    <div className="flex flex-col h-full bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <div className="flex items-center justify-between px-6 pt-4 pb-2">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">快速记录</h1>
          <p className="text-gray-600 text-sm">语音转文字，AI智能整理</p>
        </div>
        <button
          onClick={() => setShowMenu(!showMenu)}
          className="p-2 hover:bg-gray-100 rounded-full transition-colors relative"
        >
          <MoreHorizontal className="w-6 h-6 text-gray-600" />
          {showMenu && (
            <div className="absolute right-0 top-12 bg-white rounded-2xl shadow-lg border border-gray-100 py-2 w-48 z-10">
              <button className="w-full text-left px-4 py-3 hover:bg-gray-50 text-sm">导出记录</button>
              <button className="w-full text-left px-4 py-3 hover:bg-gray-50 text-sm">语音设置</button>
              <button className="w-full text-left px-4 py-3 hover:bg-gray-50 text-sm">关于应用</button>
            </div>
          )}
        </button>
      </div>

      <div className="flex-1 px-6 pb-6 space-y-6">
        {/* Voice Recording Section */}
        <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-sm border border-white/50">
          <div className="text-center">
            <div className="relative mb-8">
              <button
                onClick={handleRecording}
                className={`w-28 h-28 rounded-full flex items-center justify-center transition-all duration-300 shadow-lg ${
                  isRecording
                    ? "bg-red-500 shadow-red-200 scale-110 animate-pulse"
                    : "bg-blue-500 hover:bg-blue-600 shadow-blue-200 hover:scale-105"
                }`}
              >
                {isRecording ? <MicOff className="w-12 h-12 text-white" /> : <Mic className="w-12 h-12 text-white" />}
              </button>
              {isRecording && (
                <>
                  <div className="absolute -inset-4 rounded-full border-4 border-red-300 animate-ping opacity-75"></div>
                  <div className="absolute -inset-2 rounded-full border-2 border-red-400 animate-pulse"></div>
                </>
              )}
            </div>

            {isRecording && (
              <div className="text-red-500 font-mono text-2xl mb-4 font-bold">{formatTime(recordingTime)}</div>
            )}

            <p className="text-gray-600 font-medium">{isRecording ? "正在录音中..." : "轻触开始语音记录"}</p>
          </div>

          {transcription && (
            <div className="mt-8 p-6 bg-gray-50/80 rounded-2xl border border-gray-100">
              <h3 className="font-semibold text-gray-800 mb-3 flex items-center">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                转录文本
              </h3>
              <p className="text-gray-700 leading-relaxed">{transcription}</p>
            </div>
          )}
        </div>

        {/* Text Input Section */}
        <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-6 shadow-sm border border-white/50">
          <h3 className="font-semibold text-gray-800 mb-4">文字输入</h3>
          <textarea
            value={textInput}
            onChange={(e) => setTextInput(e.target.value)}
            placeholder="或者直接输入文字记录..."
            className="w-full h-32 p-4 border border-gray-200 rounded-2xl resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white/70 backdrop-blur-sm"
          />
        </div>

        {/* AI Tags Section */}
        {(tags.length > 0 || isProcessing) && (
          <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-6 shadow-sm border border-white/50">
            <div className="flex items-center mb-4">
              <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mr-3">
                <Sparkles className="w-4 h-4 text-white" />
              </div>
              <h3 className="font-semibold text-gray-800">AI智能标签</h3>
            </div>

            {isProcessing ? (
              <div className="flex items-center text-gray-500">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-purple-500 mr-3"></div>
                AI正在分析内容...
              </div>
            ) : (
              <div className="flex flex-wrap gap-3">
                {tags.map((tag, index) => (
                  <span
                    key={index}
                    className="px-4 py-2 bg-gradient-to-r from-purple-100 to-pink-100 text-purple-700 rounded-full text-sm font-medium border border-purple-200"
                  >
                    #{tag}
                  </span>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Save Button */}
        {(transcription || textInput.trim()) && (
          <button
            onClick={handleSave}
            className="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-semibold py-4 px-6 rounded-2xl flex items-center justify-center transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-[1.02]"
          >
            <Send className="w-5 h-5 mr-3" />
            保存到iCloud
          </button>
        )}
      </div>
    </div>
  )
}
