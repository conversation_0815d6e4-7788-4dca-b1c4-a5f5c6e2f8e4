"use client"

import { useState } from "react"
import { Search, Plus, Clock, ChevronRight, Sparkles, ChevronDown, ChevronUp, Tag } from "lucide-react"
import AIModal from "@/components/AIModal"

interface Record {
  id: string
  content: string
  tags: string[]
  createdAt: string
}

interface MainPageProps {
  onRecordClick: (record: Record) => void
  onShowRecord: () => void
}

export default function MainPage({ onRecordClick, onShowRecord }: MainPageProps) {
  const [searchMode, setSearchMode] = useState("search") // "search" | "chat"
  const [searchQuery, setSearchQuery] = useState("")
  const [chatMessages, setChatMessages] = useState<Array<{ role: "user" | "ai"; content: string }>>([])
  const [currentMessage, setCurrentMessage] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  const [showTagFilter, setShowTagFilter] = useState(false)
  const [showSearch, setShowSearch] = useState(false) // Declare setShowSearch variable
  const [showAIModal, setShowAIModal] = useState(false)

  // 模拟从iCloud同步的数据
  const records: Record[] = [
    {
      id: "1",
      content: "今天的产品会议很有收获，我们讨论了用户体验优化和新功能开发的优先级。团队决定先专注于核心功能的完善。",
      tags: ["产品会议", "用户体验", "功能开发"],
      createdAt: "今天 14:30",
    },
    {
      id: "2",
      content: "学习笔记：SwiftUI中的状态管理最佳实践，@State、@StateObject、@ObservedObject的使用场景和区别。",
      tags: ["学习笔记", "SwiftUI", "iOS开发"],
      createdAt: "今天 10:15",
    },
    {
      id: "3",
      content: "周末计划：去书店买几本设计相关的书籍，顺便看看有没有关于用户体验设计的新书。",
      tags: ["周末计划", "阅读", "设计", "用户体验"],
      createdAt: "昨天 18:45",
    },
    {
      id: "4",
      content: "产品灵感：可以开发一个基于AI的智能笔记应用，支持多模态输入和智能整理功能。",
      tags: ["产品灵感", "AI应用", "笔记"],
      createdAt: "昨天 09:20",
    },
    {
      id: "5",
      content: "今天学习了React Native的性能优化技巧，特别是关于列表渲染和内存管理的最佳实践。",
      tags: ["学习笔记", "React Native", "性能优化"],
      createdAt: "前天 16:20",
    },
    {
      id: "6",
      content: "用户体验设计思考：如何在保持功能完整性的同时简化界面，减少用户的认知负担。",
      tags: ["用户体验", "设计思考", "产品设计"],
      createdAt: "前天 11:30",
    },
  ]

  // 获取所有标签及其出现次数
  const getAllTags = () => {
    const tagCount: { [key: string]: number } = {}
    records.forEach((record) => {
      record.tags.forEach((tag) => {
        tagCount[tag] = (tagCount[tag] || 0) + 1
      })
    })
    return Object.entries(tagCount)
      .map(([tag, count]) => ({ tag, count }))
      .sort((a, b) => b.count - a.count) // 按出现次数排序
  }

  const allTags = getAllTags()

  const filteredRecords = records.filter((record) => {
    // 搜索过滤
    if (searchQuery) {
      const matchesContent = record.content.toLowerCase().includes(searchQuery.toLowerCase())
      const matchesTags = record.tags.some((tag) => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      if (!matchesContent && !matchesTags) return false
    }

    // 标签筛选
    if (selectedTags.length > 0) {
      const hasSelectedTag = selectedTags.some((selectedTag) => record.tags.includes(selectedTag))
      if (!hasSelectedTag) return false
    }

    return true
  })

  const handleSendMessage = () => {
    if (currentMessage.trim()) {
      setChatMessages((prev) => [...prev, { role: "user", content: currentMessage }])
      setIsLoading(true)

      setTimeout(() => {
        setChatMessages((prev) => [
          ...prev,
          {
            role: "ai",
            content: `根据您在iCloud中的记录，我找到了相关信息：关于"${currentMessage}"的内容主要出现在您的产品会议和学习笔记中。您最近似乎很关注用户体验和技术学习方面的内容。`,
          },
        ])
        setIsLoading(false)
      }, 1500)

      setCurrentMessage("")
    }
  }

  const handleTagToggle = (tag: string) => {
    setSelectedTags((prev) => (prev.includes(tag) ? prev.filter((t) => t !== tag) : [...prev, tag]))
  }

  const clearTagFilter = () => {
    setSelectedTags([])
    setSearchQuery("")
  }

  return (
    <div className="flex flex-col h-full bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <div className="px-6 pt-4 pb-2">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">我的记录</h1>
            <p className="text-gray-600 text-sm">已同步到iCloud · {records.length} 条记录</p>
          </div>
          <button
            onClick={onShowRecord}
            className="p-3 rounded-2xl transition-all duration-200 bg-blue-500 text-white hover:bg-blue-600"
          >
            <Plus className="w-6 h-6" />
          </button>
        </div>

        {/* Search/Chat Section */}
        {/* Search Section */}
        <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-5 mb-4 shadow-sm border border-white/50">
          <div className="flex items-center space-x-3">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="搜索记录内容、标签..."
              className="flex-1 py-3 px-4 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white"
            />
            <button className="p-3 bg-blue-500 text-white rounded-2xl hover:bg-blue-600 transition-colors">
              <Search className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Tag Filter Section */}
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-sm border border-white/50">
          <button
            onClick={() => setShowTagFilter(!showTagFilter)}
            className="w-full flex items-center justify-between p-4 hover:bg-white/50 transition-colors rounded-2xl"
          >
            <div className="flex items-center">
              <Tag className="w-5 h-5 text-gray-600 mr-2" />
              <span className="font-medium text-gray-800">按标签筛选</span>
              {selectedTags.length > 0 && (
                <span className="ml-2 px-2 py-1 bg-blue-100 text-blue-600 rounded-full text-xs font-medium">
                  {selectedTags.length} 个已选
                </span>
              )}
            </div>
            {showTagFilter ? (
              <ChevronUp className="w-5 h-5 text-gray-400" />
            ) : (
              <ChevronDown className="w-5 h-5 text-gray-400" />
            )}
          </button>

          {showTagFilter && (
            <div className="px-4 pb-4">
              <div className="border-t border-gray-100 pt-4">
                {selectedTags.length > 0 && (
                  <div className="flex items-center justify-between mb-3">
                    <span className="text-sm text-gray-600">已选择的标签：</span>
                    <button onClick={clearTagFilter} className="text-sm text-blue-600 hover:text-blue-700 font-medium">
                      清除全部
                    </button>
                  </div>
                )}

                <div className="flex flex-wrap gap-2 max-h-32 overflow-y-auto">
                  {allTags.map(({ tag, count }) => {
                    const isSelected = selectedTags.includes(tag)
                    return (
                      <button
                        key={tag}
                        onClick={() => handleTagToggle(tag)}
                        className={`px-3 py-2 rounded-full text-sm font-medium transition-all duration-200 flex items-center ${
                          isSelected
                            ? "bg-blue-500 text-white shadow-md scale-105"
                            : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                        }`}
                      >
                        #{tag}
                        <span className={`ml-1 text-xs ${isSelected ? "text-blue-100" : "text-gray-500"}`}>
                          ({count})
                        </span>
                      </button>
                    )
                  })}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Records List */}
      <div className="flex-1 px-6 pb-24 overflow-y-auto">
        {/* 筛选结果提示 */}
        {(selectedTags.length > 0 || searchQuery) && (
          <div className="mb-4 p-3 bg-blue-50 rounded-2xl border border-blue-100">
            <div className="flex items-center justify-between">
              <div className="flex items-center text-sm text-blue-700">
                <span>筛选结果：{filteredRecords.length} 条记录</span>
                {searchQuery && <span className="ml-2">· 搜索: "{searchQuery}"</span>}
                {selectedTags.length > 0 && (
                  <span className="ml-2">· 标签: {selectedTags.map((tag) => `#${tag}`).join(", ")}</span>
                )}
              </div>
              <button
                onClick={() => {
                  setSelectedTags([])
                  setSearchQuery("")
                }}
                className="text-sm text-blue-600 hover:text-blue-700 font-medium"
              >
                清除筛选
              </button>
            </div>
          </div>
        )}

        <div className="space-y-4">
          {filteredRecords.map((record) => (
            <div
              key={record.id}
              onClick={() => onRecordClick(record)}
              className="bg-white/80 backdrop-blur-sm rounded-3xl p-5 shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer border border-white/50 hover:scale-[1.02]"
            >
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center text-gray-500 text-sm">
                  <Clock className="w-4 h-4 mr-1" />
                  {record.createdAt}
                </div>
                <ChevronRight className="w-5 h-5 text-gray-400" />
              </div>

              <p className="text-gray-800 mb-4 line-clamp-2 leading-relaxed font-medium">{record.content}</p>

              {record.tags.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {record.tags.slice(0, 3).map((tag, index) => (
                    <span
                      key={index}
                      className={`px-3 py-1 rounded-full text-xs font-medium border transition-colors ${
                        selectedTags.includes(tag)
                          ? "bg-blue-500 text-white border-blue-500"
                          : "bg-blue-50 text-blue-600 border-blue-100"
                      }`}
                    >
                      #{tag}
                    </span>
                  ))}
                  {record.tags.length > 3 && (
                    <span className="px-3 py-1 bg-gray-50 text-gray-500 rounded-full text-xs">
                      +{record.tags.length - 3}
                    </span>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>

        {filteredRecords.length === 0 && (
          <div className="text-center py-16">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Tag className="w-8 h-8 text-gray-400" />
            </div>
            <p className="text-gray-500 mb-2">没有找到符合条件的记录</p>
            <p className="text-gray-400 text-sm">尝试调整筛选条件或点击右下角按钮创建新记录</p>
          </div>
        )}
      </div>

      {/* Floating AI Button */}
      <button
        onClick={() => setShowAIModal(true)}
        className="absolute bottom-8 right-6 w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center hover:scale-110 z-10"
      >
        <Sparkles className="w-8 h-8" />
      </button>

      {/* AI Modal */}
      {showAIModal && <AIModal records={records} onClose={() => setShowAIModal(false)} />}
    </div>
  )
}
