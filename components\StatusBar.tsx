export default function StatusBar() {
  return (
    <div className="flex justify-between items-center px-6 pt-3 pb-2 bg-white">
      <div className="text-sm font-semibold">9:41</div>
      <div className="flex items-center space-x-1">
        <div className="flex space-x-1">
          <div className="w-1 h-1 bg-black rounded-full"></div>
          <div className="w-1 h-1 bg-black rounded-full"></div>
          <div className="w-1 h-1 bg-gray-300 rounded-full"></div>
          <div className="w-1 h-1 bg-gray-300 rounded-full"></div>
        </div>
        <svg className="w-6 h-4 ml-2" viewBox="0 0 24 16" fill="none">
          <rect x="2" y="3" width="20" height="10" rx="2" stroke="black" strokeWidth="1" fill="none" />
          <rect x="22" y="6" width="2" height="4" rx="1" fill="black" />
        </svg>
      </div>
    </div>
  )
}
