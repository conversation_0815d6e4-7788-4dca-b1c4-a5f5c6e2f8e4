"use client"

import { Mic, List, Search } from "lucide-react"

interface TabBarProps {
  activeTab: string
  onTabChange: (tab: string) => void
}

export default function TabBar({ activeTab, onTabChange }: TabBarProps) {
  const tabs = [
    { id: "record", icon: Mic, label: "记录" },
    { id: "list", icon: List, label: "列表" },
    { id: "search", icon: Search, label: "搜索" },
  ]

  return (
    <div className="absolute bottom-0 left-0 right-0 bg-white/95 backdrop-blur-md border-t border-gray-200">
      <div className="flex justify-around py-2 px-4">
        {tabs.map((tab) => {
          const Icon = tab.icon
          const isActive = activeTab === tab.id
          return (
            <button
              key={tab.id}
              onClick={() => onTabChange(tab.id)}
              className={`flex flex-col items-center py-3 px-6 rounded-2xl transition-all duration-200 ${
                isActive ? "text-blue-600 bg-blue-50 scale-105" : "text-gray-500 hover:text-gray-700 hover:bg-gray-50"
              }`}
            >
              <Icon size={24} strokeWidth={isActive ? 2.5 : 2} />
              <span className={`text-xs mt-1 font-medium ${isActive ? "font-semibold" : ""}`}>{tab.label}</span>
            </button>
          )
        })}
      </div>
      {/* iOS Home Indicator */}
      <div className="flex justify-center pb-2">
        <div className="w-32 h-1 bg-black rounded-full opacity-30"></div>
      </div>
    </div>
  )
}
