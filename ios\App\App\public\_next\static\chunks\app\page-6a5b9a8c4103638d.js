(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{4039:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>V});var r=s(5155),a=s(2115);function l(){return(0,r.jsxs)("div",{className:"flex justify-between items-center px-6 pt-3 pb-2 bg-white",children:[(0,r.jsx)("div",{className:"text-sm font-semibold",children:"9:41"}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsxs)("div",{className:"flex space-x-1",children:[(0,r.jsx)("div",{className:"w-1 h-1 bg-black rounded-full"}),(0,r.jsx)("div",{className:"w-1 h-1 bg-black rounded-full"}),(0,r.jsx)("div",{className:"w-1 h-1 bg-gray-300 rounded-full"}),(0,r.jsx)("div",{className:"w-1 h-1 bg-gray-300 rounded-full"})]}),(0,r.jsxs)("svg",{className:"w-6 h-4 ml-2",viewBox:"0 0 24 16",fill:"none",children:[(0,r.jsx)("rect",{x:"2",y:"3",width:"20",height:"10",rx:"2",stroke:"black",strokeWidth:"1",fill:"none"}),(0,r.jsx)("rect",{x:"22",y:"6",width:"2",height:"4",rx:"1",fill:"black"})]})]})]})}let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return t.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim()};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,a.forwardRef)((e,t)=>{let{color:s="currentColor",size:r=24,strokeWidth:l=2,absoluteStrokeWidth:n,className:d="",children:o,iconNode:x,...m}=e;return(0,a.createElement)("svg",{ref:t,...c,width:r,height:r,stroke:s,strokeWidth:n?24*Number(l)/Number(r):l,className:i("lucide",d),...m},[...x.map(e=>{let[t,s]=e;return(0,a.createElement)(t,s)}),...Array.isArray(o)?o:[o]])}),o=(e,t)=>{let s=(0,a.forwardRef)((s,r)=>{let{className:l,...c}=s;return(0,a.createElement)(d,{ref:r,iconNode:t,className:i("lucide-".concat(n(e)),l),...c})});return s.displayName="".concat(e),s},x=o("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),m=o("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]),u=o("Tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]]),h=o("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]),b=o("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]),g=o("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]),p=o("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]),f=o("Sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]]),j=o("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),v=o("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]),y=o("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]),N=o("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]),w=o("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]),k=o("Send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]);function C(e){let{records:t,onClose:s}=e,[l,n]=(0,a.useState)([]),[i,c]=(0,a.useState)(""),[d,o]=(0,a.useState)(!1),[x,m]=(0,a.useState)(null),[u,h]=(0,a.useState)(""),b=(0,a.useRef)(null),g=()=>{var e;null===(e=b.current)||void 0===e||e.scrollIntoView({behavior:"smooth"})};(0,a.useEffect)(()=>{g()},[l]);let p=()=>Date.now().toString()+Math.random().toString(36).substr(2,9),C=e=>{o(!0),setTimeout(()=>{let t="",s=e.toLowerCase();t=s.includes("学习")||s.includes("技术")?"根据您的记录分析，您最近在学习SwiftUI状态管理和React Native性能优化。您的学习方式很系统，建议继续保持记录学习笔记的习惯。我注意到您对iOS开发和用户体验都很感兴趣，这是很好的技能组合。":s.includes("工作")||s.includes("产品")?"从您的产品会议记录来看，您很关注用户体验优化和功能开发的优先级。您提到团队决定先专注核心功能完善，这是明智的产品策略。您的产品思维和技术背景结合得很好。":s.includes("总结")?"## 近期记录总结\n\n### 技术学习方面\n- **SwiftUI开发**：深入学习状态管理最佳实践\n- **React Native**：掌握性能优化和内存管理技巧\n- **iOS开发**：持续关注新技术和开发模式\n\n### 产品与工作\n- 积极参与产品会议，讨论用户体验优化\n- 关注功能开发优先级，推动核心功能完善\n- 具备良好的产品思维和技术结合能力\n\n### 个人发展\n- 保持良好的学习记录习惯\n- 制定周末阅读计划，持续自我提升\n- 对AI应用有产品灵感和创新思考\n\n您的学习轨迹显示了技术深度和产品广度的良好结合。":s.includes("写")||s.includes("帮我")?"## 基于您记录的个性化内容\n\n### 学习成长轨迹\n通过分析您的记录，我发现您是一个善于学习和总结的技术人员。从SwiftUI的状态管理到React Native的性能优化，每一个技术点都有深入的思考和实践。\n\n### 产品思维培养\n您不仅关注技术实现，更注重用户体验和产品策略。在产品会议中的积极参与体现了您的全局思维。\n\n### 未来发展建议\n1. 继续保持技术学习的深度\n2. 加强产品思维与技术能力的结合\n3. 考虑将AI技术应用到实际产品中\n\n这份内容基于您的个人记录定制，可以保存为新的记录。":'关于"'.concat(e,'"，基于您在iCloud中的记录分析：\n\n您的记录显示了对技术学习和产品设计的持续关注。从具体的技术实践到产品策略思考，都体现了您的专业成长轨迹。\n\n建议您继续保持这种学习和记录的习惯，这将是您职业发展的重要资产。'),n(e=>[...e,{role:"ai",content:t,id:p()}]),o(!1)},1500)},S=()=>{if(i.trim()){let e={role:"user",content:i,id:p()};n(t=>[...t,e]),C(i),c("")}},M=(e,t)=>{m(e),h(t)},A=()=>{if(x&&u.trim()){let e=l.findIndex(e=>e.id===x);if(-1!==e){let t=[...l];t[e]={...t[e],content:u},n(t.slice(0,e+1)),C(u)}m(null),h("")}},I=()=>{m(null),h("")},z=e=>{let t=l.findIndex(t=>t.id===e);if(t>0){let s=l[t-1];"user"===s.role&&(n(l.filter(t=>t.id!==e)),C(s.content))}},L=e=>{navigator.clipboard.writeText(e),alert("内容已复制到剪贴板")},R=e=>{let t={id:p(),content:e,tags:["AI生成","智能整理"],createdAt:"刚刚"};alert("内容已保存到iCloud记录！"),console.log("保存的记录:",t)};return(0,r.jsx)("div",{className:"absolute inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4",children:(0,r.jsxs)("div",{className:"w-full max-w-lg bg-white rounded-3xl max-h-[80vh] overflow-hidden shadow-2xl",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-100",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mr-3",children:(0,r.jsx)(f,{className:"w-5 h-5 text-white"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"AI 助手"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"基于您的记录智能对话"})]})]}),(0,r.jsx)("button",{onClick:s,className:"p-2 hover:bg-gray-100 rounded-full transition-colors",children:(0,r.jsx)(j,{className:"w-6 h-6 text-gray-600"})})]}),(0,r.jsxs)("div",{className:"flex-1 p-6 max-h-96 overflow-y-auto",children:[0===l.length?(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-gradient-to-r from-purple-100 to-pink-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)(f,{className:"w-8 h-8 text-purple-500"})}),(0,r.jsx)("h3",{className:"font-semibold text-gray-800 mb-2",children:"AI智能助手"}),(0,r.jsx)("p",{className:"text-gray-600 text-sm",children:"基于您的记录内容，回答任何问题"})]}):(0,r.jsxs)("div",{className:"space-y-4",children:[l.map((e,t)=>(0,r.jsx)("div",{className:"flex ".concat("user"===e.role?"justify-end":"justify-start"),children:(0,r.jsx)("div",{className:"max-w-[80%] group",children:x===e.id?(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("textarea",{value:u,onChange:e=>h(e.target.value),className:"w-full p-3 border border-gray-300 rounded-2xl resize-none focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent",rows:3,autoFocus:!0}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("button",{onClick:A,className:"p-2 bg-green-100 text-green-700 rounded-full hover:bg-green-200 transition-colors",title:"保存",children:(0,r.jsx)(v,{className:"w-4 h-4"})}),(0,r.jsx)("button",{onClick:I,className:"p-2 bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200 transition-colors",title:"取消",children:(0,r.jsx)(j,{className:"w-4 h-4"})})]})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"px-5 py-3 rounded-3xl transition-all duration-200 ".concat("user"===e.role?"bg-purple-500 text-white cursor-pointer hover:bg-purple-600":"bg-gray-100 text-gray-800"),onClick:"user"===e.role?()=>M(e.id,e.content):void 0,children:(0,r.jsx)("div",{className:"whitespace-pre-wrap",children:e.content})}),"ai"===e.role&&(0,r.jsx)("div",{className:"mt-2 px-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200",children:(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)("button",{onClick:()=>z(e.id),className:"p-2 bg-orange-100 text-orange-700 rounded-full hover:bg-orange-200 transition-colors",disabled:d,title:"重新生成",children:(0,r.jsx)(y,{className:"w-4 h-4"})}),(0,r.jsx)("button",{onClick:()=>L(e.content),className:"p-2 bg-blue-100 text-blue-700 rounded-full hover:bg-blue-200 transition-colors",title:"复制",children:(0,r.jsx)(N,{className:"w-4 h-4"})}),(0,r.jsx)("button",{onClick:()=>R(e.content),className:"p-2 bg-green-100 text-green-700 rounded-full hover:bg-green-200 transition-colors",title:"保存到记录",children:(0,r.jsx)(w,{className:"w-4 h-4"})})]})})]})})},e.id)),d&&(0,r.jsx)("div",{className:"flex justify-start",children:(0,r.jsx)("div",{className:"bg-gray-100 px-5 py-3 rounded-3xl",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)("div",{className:"flex space-x-1",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce"}),(0,r.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),(0,r.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]}),(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"AI正在思考..."})]})})})]}),(0,r.jsx)("div",{ref:b})]}),(0,r.jsx)("div",{className:"p-6 border-t border-gray-100",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("input",{type:"text",value:i,onChange:e=>c(e.target.value),placeholder:"问问AI关于您的记录...",className:"flex-1 py-4 px-5 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white",onKeyPress:e=>"Enter"===e.key&&S(),disabled:d}),(0,r.jsx)("button",{onClick:S,disabled:d||!i.trim(),className:"p-4 bg-purple-500 hover:bg-purple-600 disabled:opacity-50 text-white rounded-2xl transition-colors shadow-md hover:shadow-lg",children:(0,r.jsx)(k,{className:"w-5 h-5"})})]})})]})})}function S(e){let{onRecordClick:t,onShowRecord:s}=e,[l,n]=(0,a.useState)("search"),[i,c]=(0,a.useState)(""),[d,o]=(0,a.useState)([]),[j,v]=(0,a.useState)(""),[y,N]=(0,a.useState)(!1),[w,k]=(0,a.useState)([]),[S,M]=(0,a.useState)(!1),[A,I]=(0,a.useState)(!1),[z,L]=(0,a.useState)(!1),R=[{id:"1",content:"今天的产品会议很有收获，我们讨论了用户体验优化和新功能开发的优先级。团队决定先专注于核心功能的完善。",tags:["产品会议","用户体验","功能开发"],createdAt:"今天 14:30"},{id:"2",content:"学习笔记：SwiftUI中的状态管理最佳实践，@State、@StateObject、@ObservedObject的使用场景和区别。",tags:["学习笔记","SwiftUI","iOS开发"],createdAt:"今天 10:15"},{id:"3",content:"周末计划：去书店买几本设计相关的书籍，顺便看看有没有关于用户体验设计的新书。",tags:["周末计划","阅读","设计","用户体验"],createdAt:"昨天 18:45"},{id:"4",content:"产品灵感：可以开发一个基于AI的智能笔记应用，支持多模态输入和智能整理功能。",tags:["产品灵感","AI应用","笔记"],createdAt:"昨天 09:20"},{id:"5",content:"今天学习了React Native的性能优化技巧，特别是关于列表渲染和内存管理的最佳实践。",tags:["学习笔记","React Native","性能优化"],createdAt:"前天 16:20"},{id:"6",content:"用户体验设计思考：如何在保持功能完整性的同时简化界面，减少用户的认知负担。",tags:["用户体验","设计思考","产品设计"],createdAt:"前天 11:30"}],E=(()=>{let e={};return R.forEach(t=>{t.tags.forEach(t=>{e[t]=(e[t]||0)+1})}),Object.entries(e).map(e=>{let[t,s]=e;return{tag:t,count:s}}).sort((e,t)=>t.count-e.count)})(),O=R.filter(e=>{if(i){let t=e.content.toLowerCase().includes(i.toLowerCase()),s=e.tags.some(e=>e.toLowerCase().includes(i.toLowerCase()));if(!t&&!s)return!1}return!(w.length>0)||!!w.some(t=>e.tags.includes(t))}),q=e=>{k(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])};return(0,r.jsxs)("div",{className:"flex flex-col h-full bg-gradient-to-br from-blue-50 via-white to-purple-50",children:[(0,r.jsxs)("div",{className:"px-6 pt-4 pb-2",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"我的记录"}),(0,r.jsxs)("p",{className:"text-gray-600 text-sm",children:["已同步到iCloud \xb7 ",R.length," 条记录"]})]}),(0,r.jsx)("button",{onClick:s,className:"p-3 rounded-2xl transition-all duration-200 bg-blue-500 text-white hover:bg-blue-600",children:(0,r.jsx)(x,{className:"w-6 h-6"})})]}),(0,r.jsx)("div",{className:"bg-white/80 backdrop-blur-sm rounded-3xl p-5 mb-4 shadow-sm border border-white/50",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("input",{type:"text",value:i,onChange:e=>c(e.target.value),placeholder:"搜索记录内容、标签...",className:"flex-1 py-3 px-4 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white"}),(0,r.jsx)("button",{className:"p-3 bg-blue-500 text-white rounded-2xl hover:bg-blue-600 transition-colors",children:(0,r.jsx)(m,{className:"w-5 h-5"})})]})}),(0,r.jsxs)("div",{className:"bg-white/80 backdrop-blur-sm rounded-2xl shadow-sm border border-white/50",children:[(0,r.jsxs)("button",{onClick:()=>M(!S),className:"w-full flex items-center justify-between p-4 hover:bg-white/50 transition-colors rounded-2xl",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(u,{className:"w-5 h-5 text-gray-600 mr-2"}),(0,r.jsx)("span",{className:"font-medium text-gray-800",children:"按标签筛选"}),w.length>0&&(0,r.jsxs)("span",{className:"ml-2 px-2 py-1 bg-blue-100 text-blue-600 rounded-full text-xs font-medium",children:[w.length," 个已选"]})]}),S?(0,r.jsx)(h,{className:"w-5 h-5 text-gray-400"}):(0,r.jsx)(b,{className:"w-5 h-5 text-gray-400"})]}),S&&(0,r.jsx)("div",{className:"px-4 pb-4",children:(0,r.jsxs)("div",{className:"border-t border-gray-100 pt-4",children:[w.length>0&&(0,r.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"已选择的标签："}),(0,r.jsx)("button",{onClick:()=>{k([]),c("")},className:"text-sm text-blue-600 hover:text-blue-700 font-medium",children:"清除全部"})]}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2 max-h-32 overflow-y-auto",children:E.map(e=>{let{tag:t,count:s}=e,a=w.includes(t);return(0,r.jsxs)("button",{onClick:()=>q(t),className:"px-3 py-2 rounded-full text-sm font-medium transition-all duration-200 flex items-center ".concat(a?"bg-blue-500 text-white shadow-md scale-105":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:["#",t,(0,r.jsxs)("span",{className:"ml-1 text-xs ".concat(a?"text-blue-100":"text-gray-500"),children:["(",s,")"]})]},t)})})]})})]})]}),(0,r.jsxs)("div",{className:"flex-1 px-6 pb-24 overflow-y-auto",children:[(w.length>0||i)&&(0,r.jsx)("div",{className:"mb-4 p-3 bg-blue-50 rounded-2xl border border-blue-100",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center text-sm text-blue-700",children:[(0,r.jsxs)("span",{children:["筛选结果：",O.length," 条记录"]}),i&&(0,r.jsxs)("span",{className:"ml-2",children:['\xb7 搜索: "',i,'"']}),w.length>0&&(0,r.jsxs)("span",{className:"ml-2",children:["\xb7 标签: ",w.map(e=>"#".concat(e)).join(", ")]})]}),(0,r.jsx)("button",{onClick:()=>{k([]),c("")},className:"text-sm text-blue-600 hover:text-blue-700 font-medium",children:"清除筛选"})]})}),(0,r.jsx)("div",{className:"space-y-4",children:O.map(e=>(0,r.jsxs)("div",{onClick:()=>t(e),className:"bg-white/80 backdrop-blur-sm rounded-3xl p-5 shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer border border-white/50 hover:scale-[1.02]",children:[(0,r.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,r.jsxs)("div",{className:"flex items-center text-gray-500 text-sm",children:[(0,r.jsx)(g,{className:"w-4 h-4 mr-1"}),e.createdAt]}),(0,r.jsx)(p,{className:"w-5 h-5 text-gray-400"})]}),(0,r.jsx)("p",{className:"text-gray-800 mb-4 line-clamp-2 leading-relaxed font-medium",children:e.content}),e.tags.length>0&&(0,r.jsxs)("div",{className:"flex flex-wrap gap-2",children:[e.tags.slice(0,3).map((e,t)=>(0,r.jsxs)("span",{className:"px-3 py-1 rounded-full text-xs font-medium border transition-colors ".concat(w.includes(e)?"bg-blue-500 text-white border-blue-500":"bg-blue-50 text-blue-600 border-blue-100"),children:["#",e]},t)),e.tags.length>3&&(0,r.jsxs)("span",{className:"px-3 py-1 bg-gray-50 text-gray-500 rounded-full text-xs",children:["+",e.tags.length-3]})]})]},e.id))}),0===O.length&&(0,r.jsxs)("div",{className:"text-center py-16",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)(u,{className:"w-8 h-8 text-gray-400"})}),(0,r.jsx)("p",{className:"text-gray-500 mb-2",children:"没有找到符合条件的记录"}),(0,r.jsx)("p",{className:"text-gray-400 text-sm",children:"尝试调整筛选条件或点击右下角按钮创建新记录"})]})]}),(0,r.jsx)("button",{onClick:()=>L(!0),className:"absolute bottom-8 right-6 w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center hover:scale-110 z-10",children:(0,r.jsx)(f,{className:"w-8 h-8"})}),z&&(0,r.jsx)(C,{records:R,onClose:()=>L(!1)})]})}let M=o("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]),A=o("PenLine",[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",key:"1ykcvy"}]]),I=o("Share",[["path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8",key:"1b2hhj"}],["polyline",{points:"16 6 12 2 8 6",key:"m901s6"}],["line",{x1:"12",x2:"12",y1:"2",y2:"15",key:"1p0rca"}]]),z=o("Cloud",[["path",{d:"M17.5 19H9a7 7 0 1 1 6.71-9h1.79a4.5 4.5 0 1 1 0 9Z",key:"p7xjir"}]]),L=o("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);function R(e){let{record:t,onBack:s}=e,[l,n]=(0,a.useState)(!1),[i,c]=(0,a.useState)(t.content),[d,o]=(0,a.useState)(t.tags),[x,m]=(0,a.useState)(""),[h,b]=(0,a.useState)(!1),p=()=>{x.trim()&&!d.includes(x.trim())&&(o([...d,x.trim()]),m(""))},f=e=>{o(d.filter(t=>t!==e))};return(0,r.jsxs)("div",{className:"flex flex-col h-full bg-gradient-to-br from-white to-gray-50",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-6 bg-white/80 backdrop-blur-sm border-b border-gray-100",children:[(0,r.jsx)("button",{onClick:s,className:"p-2 hover:bg-gray-100 rounded-2xl transition-colors",children:(0,r.jsx)(M,{className:"w-6 h-6 text-gray-600"})}),(0,r.jsx)("h1",{className:"text-lg font-semibold text-gray-800",children:"记录详情"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("button",{onClick:()=>n(!l),className:"p-2 hover:bg-gray-100 rounded-2xl transition-colors",children:(0,r.jsx)(A,{className:"w-5 h-5 text-gray-600"})}),(0,r.jsx)("button",{onClick:()=>{navigator.share?navigator.share({title:"我的记录",text:i}):(navigator.clipboard.writeText(i),alert("内容已复制到剪贴板"))},className:"p-2 hover:bg-gray-100 rounded-2xl transition-colors",children:(0,r.jsx)(I,{className:"w-5 h-5 text-gray-600"})})]})]}),(0,r.jsxs)("div",{className:"flex-1 p-6 overflow-y-auto space-y-6 pb-20",children:[(0,r.jsx)("div",{className:"bg-white/80 backdrop-blur-sm rounded-3xl p-5 shadow-sm border border-white/50",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,r.jsx)(g,{className:"w-4 h-4 mr-1"}),t.createdAt]})}),(0,r.jsxs)("div",{className:"flex items-center text-xs text-gray-500",children:[(0,r.jsx)(z,{className:"w-4 h-4 mr-1"}),"已同步"]})]})}),(0,r.jsxs)("div",{className:"bg-white/80 backdrop-blur-sm rounded-3xl p-6 shadow-sm border border-white/50",children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-800 mb-4",children:"内容"}),l?(0,r.jsx)("textarea",{value:i,onChange:e=>c(e.target.value),className:"w-full h-48 p-4 border border-gray-200 rounded-2xl resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white/70 backdrop-blur-sm"}):(0,r.jsx)("div",{className:"p-5 bg-gray-50/80 rounded-2xl border border-gray-100",children:(0,r.jsx)("p",{className:"text-gray-800 leading-relaxed whitespace-pre-wrap",children:i})})]}),(0,r.jsxs)("div",{className:"bg-white/80 backdrop-blur-sm rounded-3xl p-6 shadow-sm border border-white/50",children:[(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[(0,r.jsx)(u,{className:"w-5 h-5 text-gray-600 mr-2"}),(0,r.jsx)("h3",{className:"font-semibold text-gray-800",children:"标签"})]}),(0,r.jsx)("div",{className:"flex flex-wrap gap-3 mb-4",children:d.map((e,t)=>(0,r.jsxs)("span",{className:"px-4 py-2 rounded-full text-sm font-medium flex items-center transition-colors ".concat(l?"bg-red-50 text-red-600 border border-red-200":"bg-blue-50 text-blue-600 border border-blue-200"),children:["#",e,l&&(0,r.jsx)("button",{onClick:()=>f(e),className:"ml-2 text-red-400 hover:text-red-600 font-bold",children:"\xd7"})]},t))}),l&&(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("input",{type:"text",value:x,onChange:e=>m(e.target.value),placeholder:"添加新标签",className:"flex-1 py-3 px-4 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white/70 backdrop-blur-sm",onKeyPress:e=>"Enter"===e.key&&p()}),(0,r.jsx)("button",{onClick:p,className:"bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-2xl transition-colors font-medium",children:"添加"})]})]}),l&&(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("button",{onClick:()=>{b(!0),setTimeout(()=>{n(!1),b(!1),alert("已同步到iCloud！")},1e3)},disabled:h,className:"w-full bg-blue-500 hover:bg-blue-600 disabled:opacity-50 text-white py-4 px-4 rounded-2xl font-medium transition-colors flex items-center justify-center shadow-md",children:h?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"}),"同步中..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(w,{className:"w-5 h-5 mr-2"}),"保存到iCloud"]})}),(0,r.jsx)("button",{onClick:()=>n(!1),className:"w-full py-4 px-4 border border-gray-200 text-gray-600 rounded-2xl font-medium hover:bg-gray-50 transition-colors",children:"取消编辑"})]}),!l&&(0,r.jsxs)("button",{onClick:()=>{confirm("确定要删除这条记录吗？删除后将从iCloud中移除。")&&s()},className:"w-full bg-red-50 hover:bg-red-100 text-red-600 py-4 px-4 rounded-2xl font-medium transition-colors flex items-center justify-center border border-red-200",children:[(0,r.jsx)(L,{className:"w-5 h-5 mr-2"}),"从iCloud删除"]})]})]})}let E=o("Mic",[["path",{d:"M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z",key:"131961"}],["path",{d:"M19 10v2a7 7 0 0 1-14 0v-2",key:"1vc78b"}],["line",{x1:"12",x2:"12",y1:"19",y2:"22",key:"x3vr5v"}]]);function O(e){let{onClose:t,onStartRecording:s}=e,[l,n]=(0,a.useState)(""),[i,c]=(0,a.useState)([]),[d,o]=(0,a.useState)(!1),[x,m]=(0,a.useState)(!0),u=e=>{o(!0),setTimeout(()=>{c(["AI生成","智能整理","文字记录"]),o(!1)},2e3)};return(0,r.jsx)("div",{className:"absolute inset-0 bg-black/50 backdrop-blur-sm flex items-end z-50",children:(0,r.jsxs)("div",{className:"w-full bg-white rounded-t-3xl max-h-[85%] overflow-y-auto",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-100",children:[(0,r.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"新建记录"}),(0,r.jsx)("button",{onClick:t,className:"p-2 hover:bg-gray-100 rounded-full transition-colors",children:(0,r.jsx)(j,{className:"w-6 h-6 text-gray-600"})})]}),(0,r.jsxs)("div",{className:"p-6 space-y-6",children:[x&&(0,r.jsx)("div",{className:"flex flex-col items-center py-8",children:(0,r.jsxs)("div",{className:"relative flex flex-col items-center",children:[(0,r.jsxs)("button",{onClick:s,className:"relative w-32 h-32 rounded-full flex items-center justify-center transition-all duration-300 transform bg-gradient-to-br from-blue-500 to-blue-600 shadow-2xl shadow-blue-500/40 hover:scale-105 hover:shadow-3xl hover:shadow-blue-500/50 active:scale-95",children:[(0,r.jsx)("div",{className:"absolute inset-2 rounded-full bg-blue-400/30 blur-sm"}),(0,r.jsx)("div",{className:"relative z-10",children:(0,r.jsx)(E,{className:"w-12 h-12 text-white drop-shadow-lg",strokeWidth:2.5})})]}),(0,r.jsxs)("div",{className:"mt-6 text-center",children:[(0,r.jsx)("p",{className:"text-lg font-semibold text-gray-800 mb-1",children:"轻触开始语音记录"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"支持实时语音转文字"})]})]})}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("h3",{className:"font-semibold text-gray-800 flex items-center",children:[(0,r.jsx)("span",{className:"w-2 h-2 bg-blue-500 rounded-full mr-3"}),x?"或者直接输入文字":"文字输入"]}),(0,r.jsx)("textarea",{value:l,onChange:e=>{let t=e.target.value;n(t),t.trim().length>0?(m(!1),t.trim().length>10&&u(t)):(m(!0),c([]))},placeholder:"输入您的想法和记录...",className:"w-full h-32 p-4 border border-gray-200 rounded-2xl resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50/50 transition-colors"})]}),(i.length>0||d)&&(0,r.jsxs)("div",{className:"bg-gradient-to-r from-purple-50 to-pink-50 rounded-3xl p-6 border border-purple-200",children:[(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mr-3",children:(0,r.jsx)(f,{className:"w-4 h-4 text-white"})}),(0,r.jsx)("h3",{className:"font-semibold text-gray-800",children:"AI智能标签"})]}),d?(0,r.jsxs)("div",{className:"flex items-center text-purple-600",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-purple-500 mr-3"}),(0,r.jsx)("span",{className:"font-medium",children:"AI正在分析内容..."})]}):(0,r.jsx)("div",{className:"flex flex-wrap gap-3",children:i.map((e,t)=>(0,r.jsxs)("span",{className:"px-4 py-2 bg-gradient-to-r from-purple-100 to-pink-100 text-purple-700 rounded-full text-sm font-medium border border-purple-200 shadow-sm",children:["#",e]},t))})]}),l.trim()&&(0,r.jsxs)("button",{onClick:()=>{l.trim()&&(alert("记录已保存到iCloud！"),t())},className:"w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-semibold py-4 px-6 rounded-2xl flex items-center justify-center transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]",children:[(0,r.jsx)(k,{className:"w-5 h-5 mr-3"}),"保存到iCloud"]})]})]})})}let q=o("Square",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}]]),H=o("Play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]]),T=o("Pause",[["rect",{x:"14",y:"4",width:"4",height:"16",rx:"1",key:"zuxfzm"}],["rect",{x:"6",y:"4",width:"4",height:"16",rx:"1",key:"1okwgv"}]]);function P(e){let{onBack:t,onSave:s}=e,[l,n]=(0,a.useState)(!0),[i,c]=(0,a.useState)(!1),[d,o]=(0,a.useState)(0),[x,m]=(0,a.useState)(""),[u,h]=(0,a.useState)([]),b=(0,a.useRef)(),g=(0,a.useRef)(),p=(0,a.useRef)(),j=["今天参加了一个","关于AI技术发展的","研讨会，","了解了最新的","大语言模型","应用趋势","和技术挑战。","会议中讨论了","如何将AI技术","更好地应用到","实际产品中，","特别是在","用户体验优化","方面的应用。"],v=()=>{h(Array.from({length:100},()=>100*Math.random()))},y=()=>{let e=0;p.current=setInterval(()=>{e<j.length?(m(t=>t+j[e]+" "),e++):p.current&&clearInterval(p.current)},2e3)};return(0,a.useEffect)(()=>{v(),y()},[]),(0,a.useEffect)(()=>(l&&!i?(b.current=setInterval(()=>{o(e=>e+.01)},10),g.current=setInterval(()=>{v()},100)):(b.current&&clearInterval(b.current),g.current&&clearInterval(g.current)),()=>{b.current&&clearInterval(b.current),g.current&&clearInterval(g.current)}),[l,i]),(0,r.jsxs)("div",{className:"flex flex-col h-full bg-white",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between px-6 pt-4 pb-2",children:[(0,r.jsx)("button",{onClick:t,className:"p-2 hover:bg-gray-100 rounded-full transition-colors",children:(0,r.jsx)(M,{className:"w-6 h-6 text-gray-600"})}),(0,r.jsx)("h1",{className:"text-lg font-semibold text-gray-800",children:"录音转文字"}),(0,r.jsx)("div",{className:"w-10"})]}),(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)("div",{className:"text-6xl font-mono font-light text-gray-800 mb-2",children:(e=>{let t=Math.floor(e/60),s=Math.floor(e%60),r=Math.floor(e%1*100);return"".concat(t.toString().padStart(2,"0"),":").concat(s.toString().padStart(2,"0"),".").concat(r.toString().padStart(2,"0"))})(d)}),(0,r.jsxs)("div",{className:"flex items-center justify-center",children:[(0,r.jsx)("div",{className:"w-3 h-3 rounded-full mr-2 ".concat(l&&!i?"bg-red-500 animate-pulse":"bg-gray-400")}),(0,r.jsx)("span",{className:"text-gray-500 text-sm",children:l&&!i?"正在录音...":i?"录音已暂停":"录音已结束"})]})]}),(0,r.jsxs)("div",{className:"px-6 py-4",children:[(0,r.jsxs)("div",{className:"relative h-24 bg-gray-50 rounded-2xl overflow-hidden",children:[(0,r.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,r.jsx)("div",{className:"flex items-end space-x-1 h-16",children:u.map((e,t)=>(0,r.jsx)("div",{className:"w-1 rounded-full transition-all duration-100 ".concat(l&&!i?"bg-blue-500":"bg-gray-300"),style:{height:"".concat(Math.max(2,.4*e),"px"),opacity:l&&!i?.5*Math.random()+.5:.3}},t))})}),l&&!i&&(0,r.jsxs)("div",{className:"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2",children:[(0,r.jsx)("div",{className:"w-1 h-12 bg-orange-500 rounded-full"}),(0,r.jsx)("div",{className:"w-3 h-3 bg-orange-500 rounded-full -mt-1 -ml-1"})]})]}),(0,r.jsx)("div",{className:"flex justify-between mt-2 px-4",children:(()=>{let e=Math.ceil(d),t=[];for(let s=Math.max(0,e-2);s<=e+2;s++)t.push(s);return t})().map(e=>(0,r.jsx)("div",{className:"text-xs text-gray-400",children:"00:".concat(e.toString().padStart(2,"0"))},e))})]}),(0,r.jsxs)("div",{className:"flex-1 mx-6 mb-6 bg-gray-50 rounded-3xl p-6 overflow-y-auto",children:[(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mr-3",children:(0,r.jsx)(f,{className:"w-4 h-4 text-white"})}),(0,r.jsx)("h3",{className:"font-semibold text-gray-800",children:"实时转录"}),l&&!i&&(0,r.jsxs)("div",{className:"ml-2 flex items-center",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse mr-1"}),(0,r.jsx)("span",{className:"text-xs text-green-600 font-medium",children:"识别中"})]})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("div",{className:"text-blue-600 text-sm font-medium mb-2",children:"说话人1"}),x?(0,r.jsxs)("p",{className:"text-gray-800 leading-relaxed",children:[x,l&&!i&&(0,r.jsx)("span",{className:"inline-block w-2 h-5 bg-blue-500 ml-1 animate-pulse"})]}):(0,r.jsx)("p",{className:"text-gray-400 italic",children:"开始说话，AI将实时转录您的语音..."})]}),(0,r.jsx)("div",{className:"flex items-center justify-end"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-12 pb-8",children:[(0,r.jsx)("button",{onClick:()=>{n(!1),c(!1),p.current&&clearInterval(p.current),x.trim()&&setTimeout(()=>{s(x.trim(),["语音记录","AI转录","会议记录"])},500)},className:"w-20 h-20 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center transition-all duration-300 shadow-lg shadow-red-200 hover:scale-105",children:(0,r.jsx)(q,{className:"w-8 h-8 text-white",fill:"currentColor"})}),(0,r.jsx)("button",{onClick:()=>{c(!i),i?y():p.current&&clearInterval(p.current)},className:"w-16 h-16 rounded-full flex items-center justify-center transition-all duration-300 ".concat(i?"bg-gradient-to-r from-green-500 to-emerald-500 shadow-lg shadow-green-200":"bg-gradient-to-r from-orange-500 to-yellow-500 shadow-lg shadow-orange-200"," hover:scale-105"),children:i?(0,r.jsx)(H,{className:"w-6 h-6 text-white ml-1"}):(0,r.jsx)(T,{className:"w-6 h-6 text-white"})})]})]})}function V(){let[e,t]=(0,a.useState)("main"),[s,n]=(0,a.useState)(null),[i,c]=(0,a.useState)(!1);return(0,r.jsx)("div",{className:"max-w-sm mx-auto bg-black rounded-[2.5rem] p-2 shadow-2xl",children:(0,r.jsxs)("div",{className:"bg-white rounded-[2rem] overflow-hidden h-[844px] relative",children:[(0,r.jsx)(l,{}),"detail"===e&&s?(0,r.jsx)(R,{record:s,onBack:()=>t("main")}):"recording"===e?(0,r.jsx)(P,{onBack:()=>{t("main"),c(!0)},onSave:(e,s)=>{console.log("保存录音内容:",{content:e,tags:s}),alert("录音内容已保存到iCloud！"),t("main")}}):(0,r.jsx)(S,{onRecordClick:e=>{n(e),t("detail")},onShowRecord:()=>c(!0)}),i&&(0,r.jsx)(O,{onClose:()=>c(!1),onStartRecording:()=>{c(!1),t("recording")}}),(0,r.jsx)("div",{className:"absolute bottom-2 left-1/2 transform -translate-x-1/2",children:(0,r.jsx)("div",{className:"w-32 h-1 bg-black rounded-full opacity-30"})})]})})}},6847:(e,t,s)=>{Promise.resolve().then(s.bind(s,4039))}},e=>{var t=t=>e(e.s=t);e.O(0,[441,684,358],()=>t(6847)),_N_E=e.O()}]);