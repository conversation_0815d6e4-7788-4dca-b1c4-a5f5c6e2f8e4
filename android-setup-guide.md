# 🔧 Android环境配置指南

## 当前状态
✅ Android Studio已安装  
✅ Capacitor项目已配置  
❌ 环境变量未设置  

## 🚀 快速解决方案

### 方案1: 使用Android Studio（推荐）
1. Android Studio应该已经打开了项目
2. 在Android Studio中：
   - 点击顶部的 "Build" → "Make Project"
   - 等待构建完成
   - 点击绿色的 "Run" 按钮 ▶️
   - 选择模拟器或连接的设备

### 方案2: 设置环境变量
在Windows中设置以下环境变量：

```batch
# 找到你的Java安装路径，通常在：
# C:\Program Files\Java\jdk-xx.x.x
# 或 C:\Program Files\Eclipse Adoptium\jdk-xx.x.x.x-hotspot

# 找到你的Android SDK路径，通常在：
# C:\Users\<USER>\AppData\Local\Android\Sdk

# 设置环境变量（在系统属性 → 高级 → 环境变量中）：
JAVA_HOME=C:\Program Files\Java\jdk-xx.x.x
ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk
PATH=%PATH%;%JAVA_HOME%\bin;%ANDROID_HOME%\tools;%ANDROID_HOME%\platform-tools
```

### 方案3: 临时设置环境变量
在当前终端中临时设置：

```batch
# 替换为你的实际路径
set JAVA_HOME=C:\Program Files\Java\jdk-17.0.x
set ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk
set PATH=%PATH%;%JAVA_HOME%\bin;%ANDROID_HOME%\tools;%ANDROID_HOME%\platform-tools
```

## 📱 构建APK文件

设置好环境变量后，运行：

```bash
# 方法1: 使用Capacitor
npx cap run android

# 方法2: 直接使用Gradle
cd android
gradlew.bat assembleDebug

# 方法3: 构建发布版本
cd android
gradlew.bat assembleRelease
```

## 📁 APK文件位置

构建成功后，APK文件将在：
```
android/app/build/outputs/apk/debug/app-debug.apk
android/app/build/outputs/apk/release/app-release-unsigned.apk
```

## 🔍 检查安装

验证环境是否正确设置：

```bash
java -version
adb version
```

## 📲 安装到设备

### 使用ADB安装
```bash
# 连接设备并启用USB调试
adb install android/app/build/outputs/apk/debug/app-debug.apk
```

### 手动安装
1. 将APK文件传输到Android设备
2. 在设备上启用"未知来源"安装
3. 点击APK文件进行安装

## ⚡ 快速测试

如果Android Studio已经打开：
1. 确保有模拟器运行或设备连接
2. 点击 Run 按钮
3. 应用应该会自动安装并启动

## 🆘 故障排除

### Java未找到
- 确保安装了JDK（不仅仅是JRE）
- 检查JAVA_HOME路径是否正确

### Android SDK未找到
- 在Android Studio中检查SDK路径：File → Settings → Appearance & Behavior → System Settings → Android SDK
- 复制SDK路径并设置为ANDROID_HOME

### 模拟器问题
- 在Android Studio中：Tools → AVD Manager
- 创建或启动一个模拟器

## 📞 需要帮助？

如果遇到问题，请告诉我：
1. 你的Java版本和安装路径
2. Android Studio的SDK路径
3. 具体的错误信息
