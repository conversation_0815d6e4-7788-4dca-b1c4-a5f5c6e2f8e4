"use client"

import { useState } from "react"
import { Clock, ChevronRight, Filter, Calendar } from "lucide-react"

interface Record {
  id: string
  content: string
  tags: string[]
  createdAt: string
  type: "voice" | "text"
}

interface ListPageProps {
  onRecordClick: (record: Record) => void
}

export default function ListPage({ onRecordClick }: ListPageProps) {
  const [filter, setFilter] = useState("all")
  const [showFilter, setShowFilter] = useState(false)

  // 模拟从iCloud同步的数据
  const records: Record[] = [
    {
      id: "1",
      content: "今天的产品会议很有收获，我们讨论了用户体验优化和新功能开发的优先级。团队决定先专注于核心功能的完善。",
      tags: ["产品会议", "用户体验", "功能开发"],
      createdAt: "今天 14:30",
      type: "voice",
    },
    {
      id: "2",
      content: "学习笔记：SwiftUI中的状态管理最佳实践，@State、@StateObject、@ObservedObject的使用场景和区别。",
      tags: ["学习笔记", "SwiftUI", "iOS开发"],
      createdAt: "今天 10:15",
      type: "text",
    },
    {
      id: "3",
      content: "周末计划：去书店买几本设计相关的书籍，顺便看看有没有关于用户体验设计的新书。",
      tags: ["周末计划", "阅读", "设计"],
      createdAt: "昨天 18:45",
      type: "text",
    },
    {
      id: "4",
      content: "产品灵感：可以开发一个基于AI的智能笔记应用，支持多模态输入和智能整理功能。",
      tags: ["产品灵感", "AI应用", "笔记"],
      createdAt: "昨天 09:20",
      type: "voice",
    },
  ]

  const filteredRecords = records.filter((record) => {
    if (filter === "all") return true
    return record.type === filter
  })

  const getTypeIcon = (type: string) => {
    return type === "voice" ? "🎤" : "📝"
  }

  return (
    <div className="flex flex-col h-full bg-gradient-to-br from-gray-50 to-blue-50">
      {/* Header */}
      <div className="px-6 pt-4 pb-2">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">我的记录</h1>
            <p className="text-gray-600 text-sm">已同步到iCloud</p>
          </div>
          <button
            onClick={() => setShowFilter(!showFilter)}
            className="p-2 hover:bg-white/50 rounded-full transition-colors"
          >
            <Filter className="w-6 h-6 text-gray-600" />
          </button>
        </div>

        {/* Filter Tabs */}
        <div className="flex bg-white/80 backdrop-blur-sm rounded-2xl p-1 shadow-sm border border-white/50">
          {[
            { id: "all", label: "全部", count: records.length },
            { id: "voice", label: "语音", count: records.filter((r) => r.type === "voice").length },
            { id: "text", label: "文字", count: records.filter((r) => r.type === "text").length },
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setFilter(tab.id)}
              className={`flex-1 py-3 px-4 rounded-xl text-sm font-medium transition-all duration-200 ${
                filter === tab.id
                  ? "bg-blue-500 text-white shadow-md"
                  : "text-gray-600 hover:text-gray-800 hover:bg-white/50"
              }`}
            >
              {tab.label}
              <span className={`ml-1 text-xs ${filter === tab.id ? "text-blue-100" : "text-gray-400"}`}>
                ({tab.count})
              </span>
            </button>
          ))}
        </div>
      </div>

      {/* Records List */}
      <div className="flex-1 px-6 pb-6">
        <div className="space-y-4">
          {filteredRecords.map((record) => (
            <div
              key={record.id}
              onClick={() => onRecordClick(record)}
              className="bg-white/80 backdrop-blur-sm rounded-3xl p-5 shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer border border-white/50 hover:scale-[1.02]"
            >
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center">
                  <span className="text-xl mr-3">{getTypeIcon(record.type)}</span>
                  <div className="flex items-center text-gray-500 text-sm">
                    <Clock className="w-4 h-4 mr-1" />
                    {record.createdAt}
                  </div>
                </div>
                <ChevronRight className="w-5 h-5 text-gray-400" />
              </div>

              <p className="text-gray-800 mb-4 line-clamp-2 leading-relaxed font-medium">{record.content}</p>

              {record.tags.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {record.tags.slice(0, 3).map((tag, index) => (
                    <span
                      key={index}
                      className="px-3 py-1 bg-blue-50 text-blue-600 rounded-full text-xs font-medium border border-blue-100"
                    >
                      #{tag}
                    </span>
                  ))}
                  {record.tags.length > 3 && (
                    <span className="px-3 py-1 bg-gray-50 text-gray-500 rounded-full text-xs">
                      +{record.tags.length - 3}
                    </span>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>

        {filteredRecords.length === 0 && (
          <div className="text-center py-16">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Calendar className="w-8 h-8 text-gray-400" />
            </div>
            <p className="text-gray-500">暂无{filter === "all" ? "" : filter === "voice" ? "语音" : "文字"}记录</p>
          </div>
        )}
      </div>
    </div>
  )
}
