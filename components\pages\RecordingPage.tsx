"use client"

import { useState, useRef, useEffect } from "react"
import { ArrowLeft, Sparkles, Pause, Play, Square } from "lucide-react"

interface RecordingPageProps {
  onBack: () => void
  onSave: (content: string, tags: string[]) => void
}

export default function RecordingPage({ onBack, onSave }: RecordingPageProps) {
  const [isRecording, setIsRecording] = useState(true) // 进入页面就开始录音
  const [isPaused, setIsPaused] = useState(false)
  const [recordingTime, setRecordingTime] = useState(0)
  const [transcription, setTranscription] = useState("")
  const [audioWaveform, setAudioWaveform] = useState<number[]>([])
  const recordingTimer = useRef<NodeJS.Timeout>()
  const waveformTimer = useRef<NodeJS.Timeout>()
  const transcriptionTimer = useRef<NodeJS.Timeout>()

  // 模拟实时转录文字
  const mockTranscriptionParts = [
    "今天参加了一个",
    "关于AI技术发展的",
    "研讨会，",
    "了解了最新的",
    "大语言模型",
    "应用趋势",
    "和技术挑战。",
    "会议中讨论了",
    "如何将AI技术",
    "更好地应用到",
    "实际产品中，",
    "特别是在",
    "用户体验优化",
    "方面的应用。",
  ]

  // 生成随机波形数据
  const generateWaveform = () => {
    const newWaveform = Array.from({ length: 100 }, () => Math.random() * 100)
    setAudioWaveform(newWaveform)
  }

  // 实时转录模拟
  const simulateRealTimeTranscription = () => {
    let currentIndex = 0
    transcriptionTimer.current = setInterval(() => {
      if (currentIndex < mockTranscriptionParts.length) {
        setTranscription((prev) => prev + mockTranscriptionParts[currentIndex] + " ")
        currentIndex++
      } else {
        if (transcriptionTimer.current) {
          clearInterval(transcriptionTimer.current)
        }
      }
    }, 2000) // 每2秒添加一段文字
  }

  useEffect(() => {
    // 页面加载时自动开始录音
    generateWaveform()
    simulateRealTimeTranscription()
  }, [])

  useEffect(() => {
    if (isRecording && !isPaused) {
      recordingTimer.current = setInterval(() => {
        setRecordingTime((prev) => prev + 0.01)
      }, 10)

      waveformTimer.current = setInterval(() => {
        generateWaveform()
      }, 100)
    } else {
      if (recordingTimer.current) {
        clearInterval(recordingTimer.current)
      }
      if (waveformTimer.current) {
        clearInterval(waveformTimer.current)
      }
    }

    return () => {
      if (recordingTimer.current) {
        clearInterval(recordingTimer.current)
      }
      if (waveformTimer.current) {
        clearInterval(waveformTimer.current)
      }
    }
  }, [isRecording, isPaused])

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    const centiseconds = Math.floor((seconds % 1) * 100)
    return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}.${centiseconds
      .toString()
      .padStart(2, "0")}`
  }

  const handlePauseResume = () => {
    setIsPaused(!isPaused)
    if (isPaused) {
      // 继续录音时继续转录
      simulateRealTimeTranscription()
    } else {
      // 暂停时停止转录
      if (transcriptionTimer.current) {
        clearInterval(transcriptionTimer.current)
      }
    }
  }

  const handleStopRecording = () => {
    setIsRecording(false)
    setIsPaused(false)

    // 停止所有计时器
    if (transcriptionTimer.current) {
      clearInterval(transcriptionTimer.current)
    }

    // 如果转录内容不完整，补充完整
    if (transcription.trim()) {
      setTimeout(() => {
        const tags = ["语音记录", "AI转录", "会议记录"]
        onSave(transcription.trim(), tags)
      }, 500)
    }
  }

  const getTimeMarkers = () => {
    const totalSeconds = Math.ceil(recordingTime)
    const markers = []
    for (let i = Math.max(0, totalSeconds - 2); i <= totalSeconds + 2; i++) {
      markers.push(i)
    }
    return markers
  }

  return (
    <div className="flex flex-col h-full bg-white">
      {/* Header */}
      <div className="flex items-center justify-between px-6 pt-4 pb-2">
        <button onClick={onBack} className="p-2 hover:bg-gray-100 rounded-full transition-colors">
          <ArrowLeft className="w-6 h-6 text-gray-600" />
        </button>
        <h1 className="text-lg font-semibold text-gray-800">录音转文字</h1>
        <div className="w-10"></div>
      </div>

      {/* Recording Time Display */}
      <div className="text-center py-8">
        <div className="text-6xl font-mono font-light text-gray-800 mb-2">{formatTime(recordingTime)}</div>
        <div className="flex items-center justify-center">
          <div
            className={`w-3 h-3 rounded-full mr-2 ${
              isRecording && !isPaused ? "bg-red-500 animate-pulse" : "bg-gray-400"
            }`}
          ></div>
          <span className="text-gray-500 text-sm">
            {isRecording && !isPaused ? "正在录音..." : isPaused ? "录音已暂停" : "录音已结束"}
          </span>
        </div>
      </div>

      {/* Waveform Visualization */}
      <div className="px-6 py-4">
        <div className="relative h-24 bg-gray-50 rounded-2xl overflow-hidden">
          {/* Waveform */}
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="flex items-end space-x-1 h-16">
              {audioWaveform.map((height, index) => (
                <div
                  key={index}
                  className={`w-1 rounded-full transition-all duration-100 ${
                    isRecording && !isPaused ? "bg-blue-500" : "bg-gray-300"
                  }`}
                  style={{
                    height: `${Math.max(2, height * 0.4)}px`,
                    opacity: isRecording && !isPaused ? Math.random() * 0.5 + 0.5 : 0.3,
                  }}
                />
              ))}
            </div>
          </div>

          {/* Current Time Indicator */}
          {isRecording && !isPaused && (
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
              <div className="w-1 h-12 bg-orange-500 rounded-full"></div>
              <div className="w-3 h-3 bg-orange-500 rounded-full -mt-1 -ml-1"></div>
            </div>
          )}
        </div>

        {/* Time Markers */}
        <div className="flex justify-between mt-2 px-4">
          {getTimeMarkers().map((second) => (
            <div key={second} className="text-xs text-gray-400">
              {`00:${second.toString().padStart(2, "0")}`}
            </div>
          ))}
        </div>
      </div>

      {/* Real-time Transcription */}
      <div className="flex-1 mx-6 mb-6 bg-gray-50 rounded-3xl p-6 overflow-y-auto">
        <div className="flex items-center mb-4">
          <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mr-3">
            <Sparkles className="w-4 h-4 text-white" />
          </div>
          <h3 className="font-semibold text-gray-800">实时转录</h3>
          {isRecording && !isPaused && (
            <div className="ml-2 flex items-center">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse mr-1"></div>
              <span className="text-xs text-green-600 font-medium">识别中</span>
            </div>
          )}
        </div>

        <div className="mb-4">
          <div className="text-blue-600 text-sm font-medium mb-2">说话人1</div>
          {transcription ? (
            <p className="text-gray-800 leading-relaxed">
              {transcription}
              {isRecording && !isPaused && (
                <span className="inline-block w-2 h-5 bg-blue-500 ml-1 animate-pulse"></span>
              )}
            </p>
          ) : (
            <p className="text-gray-400 italic">开始说话，AI将实时转录您的语音...</p>
          )}
        </div>

        <div className="flex items-center justify-end"></div>
      </div>

      {/* Control Buttons */}
      <div className="flex items-center justify-center space-x-12 pb-8">
        {/* Main Stop Button */}
        <button
          onClick={handleStopRecording}
          className="w-20 h-20 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center transition-all duration-300 shadow-lg shadow-red-200 hover:scale-105"
        >
          <Square className="w-8 h-8 text-white" fill="currentColor" />
        </button>

        {/* Pause/Resume Button */}
        <button
          onClick={handlePauseResume}
          className={`w-16 h-16 rounded-full flex items-center justify-center transition-all duration-300 ${
            isPaused
              ? "bg-gradient-to-r from-green-500 to-emerald-500 shadow-lg shadow-green-200"
              : "bg-gradient-to-r from-orange-500 to-yellow-500 shadow-lg shadow-orange-200"
          } hover:scale-105`}
        >
          {isPaused ? <Play className="w-6 h-6 text-white ml-1" /> : <Pause className="w-6 h-6 text-white" />}
        </button>
      </div>
    </div>
  )
}
