# 📱 AI Note App 移动端部署指南

## 🎉 Capacitor配置完成状态

✅ **已完成的配置**：
- Capacitor项目初始化完成
- Next.js静态导出配置完成
- iOS和Android平台已添加
- 应用权限配置完成（录音、相机、存储）
- 应用信息配置完成（名称：AI Note App，包名：com.ainote.app）

## 📁 项目结构

```
next-theme-setup/
├── android/          # Android原生项目
├── ios/              # iOS原生项目  
├── out/              # Next.js构建输出
├── capacitor.config.ts # Capacitor配置
└── ...
```

## 🔧 开发环境要求

### Android开发环境
1. **Java Development Kit (JDK)**
   - 下载并安装 JDK 11 或更高版本
   - 设置 JAVA_HOME 环境变量

2. **Android Studio**
   - 下载：https://developer.android.com/studio
   - 安装Android SDK
   - 配置Android模拟器

3. **环境变量配置**
   ```bash
   # Windows
   set JAVA_HOME=C:\Program Files\Java\jdk-11.0.x
   set ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk
   set PATH=%PATH%;%ANDROID_HOME%\tools;%ANDROID_HOME%\platform-tools
   ```

### iOS开发环境（仅macOS）
1. **Xcode**
   - 从App Store安装Xcode
   - 安装Xcode Command Line Tools

2. **CocoaPods**
   ```bash
   sudo gem install cocoapods
   ```

## 🚀 构建和部署步骤

### 1. 更新Web资源
每次修改代码后运行：
```bash
npm run build
npx cap sync
```

### 2. Android部署

#### 方法1：使用Android Studio（推荐）
```bash
npx cap open android
```
- 在Android Studio中点击"Run"按钮
- 选择模拟器或连接的设备

#### 方法2：命令行构建APK
```bash
# 构建调试版本
npx cap build android

# 构建发布版本（需要签名）
cd android
./gradlew assembleRelease
```

#### 方法3：直接安装到设备
```bash
# 确保设备已连接并开启USB调试
npx cap run android
```

### 3. iOS部署

#### 使用Xcode（推荐）
```bash
npx cap open ios
```
- 在Xcode中选择目标设备
- 点击"Run"按钮

#### 命令行运行
```bash
npx cap run ios
```

## 📦 生成安装包

### Android APK
构建完成后，APK文件位置：
```
android/app/build/outputs/apk/debug/app-debug.apk
android/app/build/outputs/apk/release/app-release.apk
```

### iOS IPA
需要通过Xcode Archive功能生成：
1. 在Xcode中选择 Product → Archive
2. 选择导出方式（Development/Ad Hoc/App Store）

## 🔍 调试和测试

### 1. Web调试
```bash
# 启动开发服务器
npm run dev
# 访问 http://localhost:3000
```

### 2. 移动端调试

#### Android
- 使用Chrome DevTools：chrome://inspect
- 或在Android Studio中查看Logcat

#### iOS  
- 使用Safari Web Inspector
- 或在Xcode中查看Console

### 3. 实时重载
```bash
# 启动实时重载服务器
npx cap run android --livereload
npx cap run ios --livereload
```

## ⚠️ 常见问题

### 1. Java/Android SDK未找到
- 确保正确安装并配置环境变量
- 重启终端/IDE

### 2. iOS构建失败
- 运行 `pod install` 在 ios/App 目录
- 确保Xcode版本兼容

### 3. 权限问题
- Android：检查 AndroidManifest.xml
- iOS：检查 Info.plist

### 4. 网络请求失败
- 检查 capacitor.config.ts 中的 server 配置
- 确保API端点可访问

## 📱 发布到应用商店

### Google Play Store
1. 生成签名的APK/AAB
2. 创建Google Play Console账户
3. 上传应用并填写商店信息

### Apple App Store
1. 在Xcode中Archive应用
2. 通过App Store Connect上传
3. 提交审核

## 🔄 持续集成

建议设置CI/CD流程：
1. 代码提交触发构建
2. 自动运行测试
3. 生成安装包
4. 部署到测试环境

## 📞 技术支持

如需帮助，请检查：
- Capacitor官方文档：https://capacitorjs.com/docs
- Ionic社区论坛：https://forum.ionicframework.com
- GitHub Issues：相关项目的issue页面
