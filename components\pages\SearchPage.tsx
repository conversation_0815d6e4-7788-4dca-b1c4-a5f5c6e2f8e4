"use client"

import { useState } from "react"
import { Search, MessageCircle, Send, Sparkles, Clock, Zap } from "lucide-react"

export default function SearchPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [chatMode, setChatMode] = useState(false)
  const [chatMessages, setChatMessages] = useState<Array<{ role: "user" | "ai"; content: string }>>([])
  const [currentMessage, setCurrentMessage] = useState("")
  const [isLoading, setIsLoading] = useState(false)

  // 模拟搜索结果
  const searchResults = [
    {
      id: "1",
      content: "今天的产品会议很有收获，我们讨论了用户体验优化和新功能开发的优先级...",
      tags: ["产品会议", "用户体验"],
      createdAt: "今天 14:30",
      relevance: 95,
    },
    {
      id: "2",
      content: "学习笔记：SwiftUI中的状态管理最佳实践...",
      tags: ["学习笔记", "SwiftUI"],
      createdAt: "今天 10:15",
      relevance: 87,
    },
  ]

  const handleSearch = () => {
    if (searchQuery.trim()) {
      // 在iCloud数据中搜索
      console.log("搜索:", searchQuery)
    }
  }

  const handleSendMessage = () => {
    if (currentMessage.trim()) {
      setChatMessages((prev) => [...prev, { role: "user", content: currentMessage }])
      setIsLoading(true)

      // 模拟AI回复
      setTimeout(() => {
        setChatMessages((prev) => [
          ...prev,
          {
            role: "ai",
            content: `根据您在iCloud中的记录，我找到了相关信息：关于"${currentMessage}"的内容主要出现在您的产品会议和学习笔记中。您最近似乎很关注用户体验和技术学习方面的内容。`,
          },
        ])
        setIsLoading(false)
      }, 1500)

      setCurrentMessage("")
    }
  }

  return (
    <div className="flex flex-col h-full bg-gradient-to-br from-purple-50 via-white to-blue-50">
      {/* Header */}
      <div className="px-6 pt-4 pb-2">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">智能搜索</h1>
        <p className="text-gray-600 text-sm mb-4">搜索记录或与AI对话</p>

        {/* Mode Toggle */}
        <div className="flex bg-white/80 backdrop-blur-sm rounded-2xl p-1 shadow-sm border border-white/50">
          <button
            onClick={() => setChatMode(false)}
            className={`flex-1 py-3 px-4 rounded-xl text-sm font-medium transition-all duration-200 flex items-center justify-center ${
              !chatMode ? "bg-blue-500 text-white shadow-md" : "text-gray-600 hover:text-gray-800 hover:bg-white/50"
            }`}
          >
            <Search className="w-4 h-4 mr-2" />
            搜索模式
          </button>
          <button
            onClick={() => setChatMode(true)}
            className={`flex-1 py-3 px-4 rounded-xl text-sm font-medium transition-all duration-200 flex items-center justify-center ${
              chatMode ? "bg-purple-500 text-white shadow-md" : "text-gray-600 hover:text-gray-800 hover:bg-white/50"
            }`}
          >
            <MessageCircle className="w-4 h-4 mr-2" />
            AI问答
          </button>
        </div>
      </div>

      <div className="flex-1 px-6 pb-6">
        {!chatMode ? (
          // Search Mode
          <>
            {/* Search Input */}
            <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-4 mb-6 shadow-sm border border-white/50">
              <div className="flex items-center space-x-3">
                <div className="flex-1 relative">
                  <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="搜索记录内容、标签..."
                    className="w-full py-4 px-5 pr-12 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white/70 backdrop-blur-sm"
                    onKeyPress={(e) => e.key === "Enter" && handleSearch()}
                  />
                  <Zap className="absolute right-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                </div>
                <button
                  onClick={handleSearch}
                  className="bg-blue-500 hover:bg-blue-600 text-white p-4 rounded-2xl transition-colors shadow-md hover:shadow-lg"
                >
                  <Search className="w-5 h-5" />
                </button>
              </div>
            </div>

            {/* Search Results */}
            {searchQuery && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="font-semibold text-gray-800">搜索结果</h3>
                  <span className="text-sm text-gray-500 bg-white/60 px-3 py-1 rounded-full">
                    {searchResults.length} 条结果
                  </span>
                </div>

                {searchResults.map((result) => (
                  <div
                    key={result.id}
                    className="bg-white/80 backdrop-blur-sm rounded-3xl p-5 shadow-sm border border-white/50"
                  >
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center text-sm text-gray-500">
                        <Clock className="w-4 h-4 mr-1" />
                        {result.createdAt}
                      </div>
                      <span className="text-xs bg-green-100 text-green-600 px-3 py-1 rounded-full font-medium">
                        {result.relevance}% 匹配
                      </span>
                    </div>
                    <p className="text-gray-800 mb-3 line-clamp-2 leading-relaxed">{result.content}</p>
                    <div className="flex flex-wrap gap-2">
                      {result.tags.map((tag, index) => (
                        <span
                          key={index}
                          className="px-3 py-1 bg-blue-50 text-blue-600 rounded-full text-xs font-medium border border-blue-100"
                        >
                          #{tag}
                        </span>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </>
        ) : (
          // Chat Mode
          <>
            {/* Chat Messages */}
            <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-5 mb-4 shadow-sm border border-white/50 flex-1 flex flex-col">
              <div className="flex items-center mb-4">
                <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mr-3">
                  <Sparkles className="w-4 h-4 text-white" />
                </div>
                <h3 className="font-semibold text-gray-800">AI 智能助手</h3>
              </div>

              <div className="flex-1 space-y-4 max-h-96 overflow-y-auto">
                {chatMessages.length === 0 ? (
                  <div className="text-center text-gray-500 py-12">
                    <div className="w-16 h-16 bg-gradient-to-r from-purple-100 to-pink-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <MessageCircle className="w-8 h-8 text-purple-500" />
                    </div>
                    <p className="font-medium">向AI提问关于您记录的任何问题</p>
                    <p className="text-sm mt-1">基于您的iCloud数据进行智能回答</p>
                  </div>
                ) : (
                  chatMessages.map((message, index) => (
                    <div key={index} className={`flex ${message.role === "user" ? "justify-end" : "justify-start"}`}>
                      <div
                        className={`max-w-xs px-5 py-3 rounded-3xl ${
                          message.role === "user" ? "bg-blue-500 text-white" : "bg-gray-100 text-gray-800"
                        }`}
                      >
                        {message.content}
                      </div>
                    </div>
                  ))
                )}
                {isLoading && (
                  <div className="flex justify-start">
                    <div className="bg-gray-100 px-5 py-3 rounded-3xl">
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                        <div
                          className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                          style={{ animationDelay: "0.1s" }}
                        ></div>
                        <div
                          className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                          style={{ animationDelay: "0.2s" }}
                        ></div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Chat Input */}
            <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-4 shadow-sm border border-white/50">
              <div className="flex items-center space-x-3">
                <input
                  type="text"
                  value={currentMessage}
                  onChange={(e) => setCurrentMessage(e.target.value)}
                  placeholder="问问AI关于您的记录..."
                  className="flex-1 py-4 px-5 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white/70 backdrop-blur-sm"
                  onKeyPress={(e) => e.key === "Enter" && handleSendMessage()}
                  disabled={isLoading}
                />
                <button
                  onClick={handleSendMessage}
                  disabled={isLoading}
                  className="bg-purple-500 hover:bg-purple-600 disabled:opacity-50 text-white p-4 rounded-2xl transition-colors shadow-md hover:shadow-lg"
                >
                  <Send className="w-5 h-5" />
                </button>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  )
}
