"use client"

import { useState } from "react"
import { User, Bell, Shield, Palette, Mic, Globe, HelpCircle, ChevronRight } from "lucide-react"

export default function SettingsPage() {
  const [notifications, setNotifications] = useState(true)
  const [autoTag, setAutoTag] = useState(true)
  const [voiceQuality, setVoiceQuality] = useState("high")

  const settingSections = [
    {
      title: "账户设置",
      items: [
        { icon: User, label: "个人资料", action: () => {} },
        { icon: Shield, label: "隐私设置", action: () => {} },
      ],
    },
    {
      title: "应用设置",
      items: [
        {
          icon: Bell,
          label: "通知提醒",
          toggle: true,
          value: notifications,
          onChange: setNotifications,
        },
        {
          icon: Palette,
          label: "AI自动标签",
          toggle: true,
          value: autoTag,
          onChange: setAutoTag,
        },
        { icon: Mic, label: "语音设置", action: () => {} },
        { icon: Globe, label: "语言设置", action: () => {} },
      ],
    },
    {
      title: "帮助与支持",
      items: [
        { icon: HelpCircle, label: "使用帮助", action: () => {} },
        { icon: Shield, label: "关于应用", action: () => {} },
      ],
    },
  ]

  return (
    <div className="flex flex-col h-full bg-gray-50 px-6 pt-6">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-800 mb-2">设置</h1>
        <p className="text-gray-600">个性化您的应用体验</p>
      </div>

      {/* User Profile Card */}
      <div className="bg-white rounded-2xl p-6 mb-6 shadow-sm">
        <div className="flex items-center">
          <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
            <User className="w-8 h-8 text-white" />
          </div>
          <div className="ml-4 flex-1">
            <h3 className="font-semibold text-gray-800">用户名</h3>
            <p className="text-gray-500 text-sm"><EMAIL></p>
          </div>
          <ChevronRight className="w-5 h-5 text-gray-400" />
        </div>
      </div>

      {/* Settings Sections */}
      <div className="flex-1 space-y-6">
        {settingSections.map((section, sectionIndex) => (
          <div key={sectionIndex} className="bg-white rounded-2xl shadow-sm overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-100">
              <h3 className="font-semibold text-gray-800">{section.title}</h3>
            </div>

            <div className="divide-y divide-gray-100">
              {section.items.map((item, itemIndex) => {
                const Icon = item.icon
                return (
                  <div
                    key={itemIndex}
                    className="px-6 py-4 flex items-center justify-between hover:bg-gray-50 transition-colors cursor-pointer"
                    onClick={item.action}
                  >
                    <div className="flex items-center">
                      <Icon className="w-5 h-5 text-gray-600 mr-3" />
                      <span className="text-gray-800">{item.label}</span>
                    </div>

                    {item.toggle ? (
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          item.onChange?.(!item.value)
                        }}
                        className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                          item.value ? "bg-blue-500" : "bg-gray-300"
                        }`}
                      >
                        <span
                          className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                            item.value ? "translate-x-6" : "translate-x-1"
                          }`}
                        />
                      </button>
                    ) : (
                      <ChevronRight className="w-5 h-5 text-gray-400" />
                    )}
                  </div>
                )
              })}
            </div>
          </div>
        ))}
      </div>

      {/* Version Info */}
      <div className="text-center py-4 text-gray-500 text-sm">版本 1.0.0</div>
    </div>
  )
}
