<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/80508adae8f7c490.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-57f139aa5b5fb95e.js"/><script src="/_next/static/chunks/4bd1b696-0c4616666186143c.js" async=""></script><script src="/_next/static/chunks/684-833328177a2885ea.js" async=""></script><script src="/_next/static/chunks/main-app-9e8c316f2a7af02b.js" async=""></script><script src="/_next/static/chunks/app/layout-088c52bad682bf46.js" async=""></script><script src="/_next/static/chunks/app/page-6a5b9a8c4103638d.js" async=""></script><title>Next.js Community Starter</title><meta name="description" content="A modern Next.js starter with theme support"/><meta name="generator" content="v0.dev"/><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__className_e8ce0c"><script>((e,t,r,n,o,a,i,u)=>{let s=document.documentElement,l=["light","dark"];function c(t){var r;(Array.isArray(e)?e:[e]).forEach(e=>{let r="class"===e,n=r&&a?o.map(e=>a[e]||e):o;r?(s.classList.remove(...n),s.classList.add(a&&a[t]?a[t]:t)):s.setAttribute(e,t)}),r=t,u&&l.includes(r)&&(s.style.colorScheme=r)}if(n)c(n);else try{let e=localStorage.getItem(t)||r,n=i&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;c(n)}catch(e){}})("class","theme","system",null,["light","dark"],null,true,true)</script><!--$--><div class="max-w-sm mx-auto bg-black rounded-[2.5rem] p-2 shadow-2xl"><div class="bg-white rounded-[2rem] overflow-hidden h-[844px] relative"><div class="flex justify-between items-center px-6 pt-3 pb-2 bg-white"><div class="text-sm font-semibold">9:41</div><div class="flex items-center space-x-1"><div class="flex space-x-1"><div class="w-1 h-1 bg-black rounded-full"></div><div class="w-1 h-1 bg-black rounded-full"></div><div class="w-1 h-1 bg-gray-300 rounded-full"></div><div class="w-1 h-1 bg-gray-300 rounded-full"></div></div><svg class="w-6 h-4 ml-2" viewBox="0 0 24 16" fill="none"><rect x="2" y="3" width="20" height="10" rx="2" stroke="black" stroke-width="1" fill="none"></rect><rect x="22" y="6" width="2" height="4" rx="1" fill="black"></rect></svg></div></div><div class="flex flex-col h-full bg-gradient-to-br from-blue-50 via-white to-purple-50"><div class="px-6 pt-4 pb-2"><div class="flex items-center justify-between mb-4"><div><h1 class="text-2xl font-bold text-gray-900">我的记录</h1><p class="text-gray-600 text-sm">已同步到iCloud · <!-- -->6<!-- --> 条记录</p></div><button class="p-3 rounded-2xl transition-all duration-200 bg-blue-500 text-white hover:bg-blue-600"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus w-6 h-6"><path d="M5 12h14"></path><path d="M12 5v14"></path></svg></button></div><div class="bg-white/80 backdrop-blur-sm rounded-3xl p-5 mb-4 shadow-sm border border-white/50"><div class="flex items-center space-x-3"><input type="text" placeholder="搜索记录内容、标签..." class="flex-1 py-3 px-4 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white" value=""/><button class="p-3 bg-blue-500 text-white rounded-2xl hover:bg-blue-600 transition-colors"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search w-5 h-5"><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path></svg></button></div></div><div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-sm border border-white/50"><button class="w-full flex items-center justify-between p-4 hover:bg-white/50 transition-colors rounded-2xl"><div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-tag w-5 h-5 text-gray-600 mr-2"><path d="M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z"></path><circle cx="7.5" cy="7.5" r=".5" fill="currentColor"></circle></svg><span class="font-medium text-gray-800">按标签筛选</span></div><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down w-5 h-5 text-gray-400"><path d="m6 9 6 6 6-6"></path></svg></button></div></div><div class="flex-1 px-6 pb-24 overflow-y-auto"><div class="space-y-4"><div class="bg-white/80 backdrop-blur-sm rounded-3xl p-5 shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer border border-white/50 hover:scale-[1.02]"><div class="flex items-start justify-between mb-3"><div class="flex items-center text-gray-500 text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clock w-4 h-4 mr-1"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>今天 14:30</div><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right w-5 h-5 text-gray-400"><path d="m9 18 6-6-6-6"></path></svg></div><p class="text-gray-800 mb-4 line-clamp-2 leading-relaxed font-medium">今天的产品会议很有收获，我们讨论了用户体验优化和新功能开发的优先级。团队决定先专注于核心功能的完善。</p><div class="flex flex-wrap gap-2"><span class="px-3 py-1 rounded-full text-xs font-medium border transition-colors bg-blue-50 text-blue-600 border-blue-100">#<!-- -->产品会议</span><span class="px-3 py-1 rounded-full text-xs font-medium border transition-colors bg-blue-50 text-blue-600 border-blue-100">#<!-- -->用户体验</span><span class="px-3 py-1 rounded-full text-xs font-medium border transition-colors bg-blue-50 text-blue-600 border-blue-100">#<!-- -->功能开发</span></div></div><div class="bg-white/80 backdrop-blur-sm rounded-3xl p-5 shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer border border-white/50 hover:scale-[1.02]"><div class="flex items-start justify-between mb-3"><div class="flex items-center text-gray-500 text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clock w-4 h-4 mr-1"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>今天 10:15</div><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right w-5 h-5 text-gray-400"><path d="m9 18 6-6-6-6"></path></svg></div><p class="text-gray-800 mb-4 line-clamp-2 leading-relaxed font-medium">学习笔记：SwiftUI中的状态管理最佳实践，@State、@StateObject、@ObservedObject的使用场景和区别。</p><div class="flex flex-wrap gap-2"><span class="px-3 py-1 rounded-full text-xs font-medium border transition-colors bg-blue-50 text-blue-600 border-blue-100">#<!-- -->学习笔记</span><span class="px-3 py-1 rounded-full text-xs font-medium border transition-colors bg-blue-50 text-blue-600 border-blue-100">#<!-- -->SwiftUI</span><span class="px-3 py-1 rounded-full text-xs font-medium border transition-colors bg-blue-50 text-blue-600 border-blue-100">#<!-- -->iOS开发</span></div></div><div class="bg-white/80 backdrop-blur-sm rounded-3xl p-5 shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer border border-white/50 hover:scale-[1.02]"><div class="flex items-start justify-between mb-3"><div class="flex items-center text-gray-500 text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clock w-4 h-4 mr-1"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>昨天 18:45</div><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right w-5 h-5 text-gray-400"><path d="m9 18 6-6-6-6"></path></svg></div><p class="text-gray-800 mb-4 line-clamp-2 leading-relaxed font-medium">周末计划：去书店买几本设计相关的书籍，顺便看看有没有关于用户体验设计的新书。</p><div class="flex flex-wrap gap-2"><span class="px-3 py-1 rounded-full text-xs font-medium border transition-colors bg-blue-50 text-blue-600 border-blue-100">#<!-- -->周末计划</span><span class="px-3 py-1 rounded-full text-xs font-medium border transition-colors bg-blue-50 text-blue-600 border-blue-100">#<!-- -->阅读</span><span class="px-3 py-1 rounded-full text-xs font-medium border transition-colors bg-blue-50 text-blue-600 border-blue-100">#<!-- -->设计</span><span class="px-3 py-1 bg-gray-50 text-gray-500 rounded-full text-xs">+<!-- -->1</span></div></div><div class="bg-white/80 backdrop-blur-sm rounded-3xl p-5 shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer border border-white/50 hover:scale-[1.02]"><div class="flex items-start justify-between mb-3"><div class="flex items-center text-gray-500 text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clock w-4 h-4 mr-1"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>昨天 09:20</div><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right w-5 h-5 text-gray-400"><path d="m9 18 6-6-6-6"></path></svg></div><p class="text-gray-800 mb-4 line-clamp-2 leading-relaxed font-medium">产品灵感：可以开发一个基于AI的智能笔记应用，支持多模态输入和智能整理功能。</p><div class="flex flex-wrap gap-2"><span class="px-3 py-1 rounded-full text-xs font-medium border transition-colors bg-blue-50 text-blue-600 border-blue-100">#<!-- -->产品灵感</span><span class="px-3 py-1 rounded-full text-xs font-medium border transition-colors bg-blue-50 text-blue-600 border-blue-100">#<!-- -->AI应用</span><span class="px-3 py-1 rounded-full text-xs font-medium border transition-colors bg-blue-50 text-blue-600 border-blue-100">#<!-- -->笔记</span></div></div><div class="bg-white/80 backdrop-blur-sm rounded-3xl p-5 shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer border border-white/50 hover:scale-[1.02]"><div class="flex items-start justify-between mb-3"><div class="flex items-center text-gray-500 text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clock w-4 h-4 mr-1"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>前天 16:20</div><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right w-5 h-5 text-gray-400"><path d="m9 18 6-6-6-6"></path></svg></div><p class="text-gray-800 mb-4 line-clamp-2 leading-relaxed font-medium">今天学习了React Native的性能优化技巧，特别是关于列表渲染和内存管理的最佳实践。</p><div class="flex flex-wrap gap-2"><span class="px-3 py-1 rounded-full text-xs font-medium border transition-colors bg-blue-50 text-blue-600 border-blue-100">#<!-- -->学习笔记</span><span class="px-3 py-1 rounded-full text-xs font-medium border transition-colors bg-blue-50 text-blue-600 border-blue-100">#<!-- -->React Native</span><span class="px-3 py-1 rounded-full text-xs font-medium border transition-colors bg-blue-50 text-blue-600 border-blue-100">#<!-- -->性能优化</span></div></div><div class="bg-white/80 backdrop-blur-sm rounded-3xl p-5 shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer border border-white/50 hover:scale-[1.02]"><div class="flex items-start justify-between mb-3"><div class="flex items-center text-gray-500 text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clock w-4 h-4 mr-1"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>前天 11:30</div><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right w-5 h-5 text-gray-400"><path d="m9 18 6-6-6-6"></path></svg></div><p class="text-gray-800 mb-4 line-clamp-2 leading-relaxed font-medium">用户体验设计思考：如何在保持功能完整性的同时简化界面，减少用户的认知负担。</p><div class="flex flex-wrap gap-2"><span class="px-3 py-1 rounded-full text-xs font-medium border transition-colors bg-blue-50 text-blue-600 border-blue-100">#<!-- -->用户体验</span><span class="px-3 py-1 rounded-full text-xs font-medium border transition-colors bg-blue-50 text-blue-600 border-blue-100">#<!-- -->设计思考</span><span class="px-3 py-1 rounded-full text-xs font-medium border transition-colors bg-blue-50 text-blue-600 border-blue-100">#<!-- -->产品设计</span></div></div></div></div><button class="absolute bottom-8 right-6 w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center hover:scale-110 z-10"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-sparkles w-8 h-8"><path d="M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z"></path><path d="M20 3v4"></path><path d="M22 5h-4"></path><path d="M4 17v2"></path><path d="M5 18H3"></path></svg></button></div><div class="absolute bottom-2 left-1/2 transform -translate-x-1/2"><div class="w-32 h-1 bg-black rounded-full opacity-30"></div></div></div></div><!--/$--><script src="/_next/static/chunks/webpack-57f139aa5b5fb95e.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[1362,[\"177\",\"static/chunks/app/layout-088c52bad682bf46.js\"],\"ThemeProvider\"]\n3:I[7555,[],\"\"]\n4:I[1295,[],\"\"]\n5:I[894,[],\"ClientPageRoot\"]\n6:I[4039,[\"974\",\"static/chunks/app/page-6a5b9a8c4103638d.js\"],\"default\"]\n9:I[9665,[],\"OutletBoundary\"]\nc:I[9665,[],\"ViewportBoundary\"]\ne:I[9665,[],\"MetadataBoundary\"]\n10:I[6614,[],\"\"]\n:HL[\"/_next/static/css/80508adae8f7c490.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"wDShU2ikYEfS5PN8hYWwt\",\"p\":\"\",\"c\":[\"\",\"\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"__PAGE__\",{}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/80508adae8f7c490.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"suppressHydrationWarning\":true,\"children\":[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c\",\"children\":[\"$\",\"$L2\",null,{\"attribute\":\"class\",\"defaultTheme\":\"system\",\"enableSystem\":true,\"disableTransitionOnChange\":true,\"children\":[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]}]}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"$L5\",null,{\"Component\":\"$6\",\"searchParams\":{},\"params\":{},\"promises\":[\"$@7\",\"$@8\"]}],\"$undefined\",null,[\"$\",\"$L9\",null,{\"children\":[\"$La\",\"$Lb\",null]}]]}],{},null,false]},[null,[],[]],false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"EvKIhjiJQ-wTCcyfVjVzr\",{\"children\":[[\"$\",\"$Lc\",null,{\"children\":\"$Ld\"}],null]}],[\"$\",\"$Le\",null,{\"children\":\"$Lf\"}]]}],false]],\"m\":\"$undefined\",\"G\":[\"$10\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"7:{}\n8:{}\n"])</script><script>self.__next_f.push([1,"d:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\na:null\n"])</script><script>self.__next_f.push([1,"b:null\nf:[[\"$\",\"title\",\"0\",{\"children\":\"Next.js Community Starter\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"A modern Next.js starter with theme support\"}],[\"$\",\"meta\",\"2\",{\"name\":\"generator\",\"content\":\"v0.dev\"}]]\n"])</script></body></html>