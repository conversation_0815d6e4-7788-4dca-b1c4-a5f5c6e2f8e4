(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{1362:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>c});var n=r(2115),s=(e,t,r,n,s,a,o,l)=>{let c=document.documentElement,m=["light","dark"];function i(t){var r;(Array.isArray(e)?e:[e]).forEach(e=>{let r="class"===e,n=r&&a?s.map(e=>a[e]||e):s;r?(c.classList.remove(...n),c.classList.add(a&&a[t]?a[t]:t)):c.setAttribute(e,t)}),r=t,l&&m.includes(r)&&(c.style.colorScheme=r)}if(n)i(n);else try{let e=localStorage.getItem(t)||r,n=o&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;i(n)}catch(e){}},a=["light","dark"],o="(prefers-color-scheme: dark)",l=n.createContext(void 0),c=e=>n.useContext(l)?n.createElement(n.Fragment,null,e.children):n.createElement(i,{...e}),m=["light","dark"],i=e=>{let{forcedTheme:t,disableTransitionOnChange:r=!1,enableSystem:s=!0,enableColorScheme:c=!0,storageKey:i="theme",themes:f=m,defaultTheme:b=s?"system":"light",attribute:p="data-theme",value:v,children:g,nonce:E,scriptProps:S}=e,[k,w]=n.useState(()=>u(i,b)),[C,T]=n.useState(()=>"system"===k?y():k),_=v?Object.values(v):f,L=n.useCallback(e=>{let t=e;if(!t)return;"system"===e&&s&&(t=y());let n=v?v[t]:t,o=r?h(E):null,l=document.documentElement,m=e=>{"class"===e?(l.classList.remove(..._),n&&l.classList.add(n)):e.startsWith("data-")&&(n?l.setAttribute(e,n):l.removeAttribute(e))};if(Array.isArray(p)?p.forEach(m):m(p),c){let e=a.includes(b)?b:null,r=a.includes(t)?t:e;l.style.colorScheme=r}null==o||o()},[E]),A=n.useCallback(e=>{let t="function"==typeof e?e(k):e;w(t);try{localStorage.setItem(i,t)}catch(e){}},[k]),P=n.useCallback(e=>{T(y(e)),"system"===k&&s&&!t&&L("system")},[k,t]);n.useEffect(()=>{let e=window.matchMedia(o);return e.addListener(P),P(e),()=>e.removeListener(P)},[P]),n.useEffect(()=>{let e=e=>{e.key===i&&(e.newValue?w(e.newValue):A(b))};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[A]),n.useEffect(()=>{L(null!=t?t:k)},[t,k]);let N=n.useMemo(()=>({theme:k,setTheme:A,forcedTheme:t,resolvedTheme:"system"===k?C:k,themes:s?[...f,"system"]:f,systemTheme:s?C:void 0}),[k,A,t,C,s,f]);return n.createElement(l.Provider,{value:N},n.createElement(d,{forcedTheme:t,storageKey:i,attribute:p,enableSystem:s,enableColorScheme:c,defaultTheme:b,value:v,themes:f,nonce:E,scriptProps:S}),g)},d=n.memo(e=>{let{forcedTheme:t,storageKey:r,attribute:a,enableSystem:o,enableColorScheme:l,defaultTheme:c,value:m,themes:i,nonce:d,scriptProps:u}=e,h=JSON.stringify([a,r,c,t,i,m,o,l]).slice(1,-1);return n.createElement("script",{...u,suppressHydrationWarning:!0,nonce:"",dangerouslySetInnerHTML:{__html:"(".concat(s.toString(),")(").concat(h,")")}})}),u=(e,t)=>{let r;try{r=localStorage.getItem(e)||void 0}catch(e){}return r||t},h=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},y=e=>(e||(e=window.matchMedia(o)),e.matches?"dark":"light")},4328:(e,t,r)=>{Promise.resolve().then(r.bind(r,1362)),Promise.resolve().then(r.t.bind(r,9840,23)),Promise.resolve().then(r.t.bind(r,9324,23))},9324:()=>{},9840:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c"}}},e=>{var t=t=>e(e.s=t);e.O(0,[385,441,684,358],()=>t(4328)),_N_E=e.O()}]);